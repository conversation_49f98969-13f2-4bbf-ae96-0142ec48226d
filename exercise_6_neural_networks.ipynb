{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exercise 6 - Neural Networks\n", "**Student:** 22BCE7897 <PERSON><PERSON><PERSON><PERSON>\n", "\n", "This notebook implements:\n", "1. <PERSON><PERSON><PERSON><PERSON> on Breast Cancer Dataset\n", "2. Multi-Layer Perceptron (MLP) on Digits Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.datasets import load_breast_cancer, load_digits\n", "from sklearn.linear_model import Perceptron\n", "from sklearn.neural_network import MLPClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (10, 6)\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON><PERSON><PERSON> on Breast Cancer Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Breast Cancer Dataset\n", "print(\"🔍 Loading Breast Cancer Dataset...\")\n", "data_bc = load_breast_cancer()\n", "X_bc = data_bc.data\n", "y_bc = data_bc.target\n", "\n", "print(f\"Dataset shape: {X_bc.shape}\")\n", "print(f\"Target shape: {y_bc.shape}\")\n", "print(f\"Feature names: {data_bc.feature_names[:5]}...\")  # Show first 5\n", "print(f\"Target names: {data_bc.target_names}\")\n", "print(f\"Class distribution: {np.bincount(y_bc)}\")\n", "print(f\"Class percentages: {np.bincount(y_bc) / len(y_bc) * 100}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Explore the dataset\n", "bc_df = pd.DataFrame(X_bc, columns=data_bc.feature_names)\n", "bc_df['target'] = y_bc\n", "\n", "print(\"Dataset Statistics:\")\n", "print(bc_df.describe().round(2))\n", "\n", "# Visualize some key features\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Feature distributions by class\n", "key_features = ['mean radius', 'mean texture', 'mean perimeter', 'mean area']\n", "\n", "for i, feature in enumerate(key_features):\n", "    row, col = i // 2, i % 2\n", "    for target in [0, 1]:\n", "        subset = bc_df[bc_df['target'] == target][feature]\n", "        axes[row, col].hist(subset, alpha=0.7, \n", "                           label=f'{data_bc.target_names[target]}', bins=20)\n", "    axes[row, col].set_xlabel(feature)\n", "    axes[row, col].set_ylabel('Frequency')\n", "    axes[row, col].set_title(f'Distribution of {feature}')\n", "    axes[row, col].legend()\n", "    axes[row, col].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split Data\n", "X_train_bc, X_test_bc, y_train_bc, y_test_bc = train_test_split(\n", "    X_bc, y_bc, test_size=0.2, random_state=42, stratify=y_bc\n", ")\n", "\n", "print(f\"Training set: {X_train_bc.shape}\")\n", "print(f\"Test set: {X_test_bc.shape}\")\n", "print(f\"Training class distribution: {np.bincount(y_train_bc)}\")\n", "print(f\"Test class distribution: {np.bincount(y_test_bc)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Standardize features (important for Perceptron)\n", "scaler_bc = StandardScaler()\n", "X_train_bc_scaled = scaler_bc.fit_transform(X_train_bc)\n", "X_test_bc_scaled = scaler_bc.transform(X_test_bc)\n", "\n", "print(f\"Original feature means: {X_train_bc.mean(axis=0)[:5].round(2)}\")\n", "print(f\"Scaled feature means: {X_train_bc_scaled.mean(axis=0)[:5].round(2)}\")\n", "print(f\"Scaled feature stds: {X_train_bc_scaled.std(axis=0)[:5].round(2)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train Perceptron\n", "print(\"🔍 Training Perceptron on Breast Cancer Dataset\")\n", "perceptron = Perceptron(max_iter=1000, tol=1e-3, random_state=42)\n", "perceptron.fit(X_train_bc_scaled, y_train_bc)\n", "\n", "# Make predictions\n", "y_pred_bc = perceptron.predict(X_test_bc_scaled)\n", "\n", "# Evaluation\n", "accuracy_bc = accuracy_score(y_test_bc, y_pred_bc)\n", "print(f\"\\nPerceptron Accuracy: {accuracy_bc:.4f}\")\n", "\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test_bc, y_pred_bc, target_names=data_bc.target_names))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for Perceptron\n", "cm_bc = confusion_matrix(y_test_bc, y_pred_bc)\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm_bc, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=data_bc.target_names, \n", "            yticklabels=data_bc.target_names)\n", "plt.title('Perceptron Confusion Matrix - Breast Cancer Classification')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.show()\n", "\n", "# Additional metrics\n", "tn, fp, fn, tp = cm_bc.ravel()\n", "sensitivity = tp / (tp + fn)  # Recall for malignant\n", "specificity = tn / (tn + fp)  # Recall for benign\n", "precision = tp / (tp + fp)    # Precision for malignant\n", "\n", "print(f\"\\nDetailed Metrics:\")\n", "print(f\"Sensitivity (Recall for Malignant): {sensitivity:.4f}\")\n", "print(f\"Specificity (Recall for Benign): {specificity:.4f}\")\n", "print(f\"Precision for Malignant: {precision:.4f}\")\n", "print(f\"Number of iterations: {perceptron.n_iter_}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Multi-Layer Perceptron (MLP) on Digits Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Digits Dataset\n", "print(\"🔍 Loading Digits Dataset...\")\n", "digits = load_digits()\n", "X_digits = digits.data\n", "y_digits = digits.target\n", "\n", "print(f\"Dataset shape: {X_digits.shape}\")\n", "print(f\"Target shape: {y_digits.shape}\")\n", "print(f\"Number of classes: {len(digits.target_names)}\")\n", "print(f\"Classes: {digits.target_names}\")\n", "print(f\"Image shape: {digits.images[0].shape}\")\n", "print(f\"Class distribution: {np.bincount(y_digits)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize some digit samples\n", "fig, axes = plt.subplots(2, 5, figsize=(12, 6))\n", "for i in range(10):\n", "    row, col = i // 5, i % 5\n", "    # Find first occurrence of each digit\n", "    idx = np.where(y_digits == i)[0][0]\n", "    axes[row, col].imshow(digits.images[idx], cmap='gray')\n", "    axes[row, col].set_title(f'Digit: {i}')\n", "    axes[row, col].axis('off')\n", "\n", "plt.suptitle('Sample Digits from Dataset')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Normalize data (MLP works better with scaled data)\n", "scaler_digits = MinMaxScaler()\n", "X_digits_scaled = scaler_digits.fit_transform(X_digits)\n", "\n", "print(f\"Original data range: [{X_digits.min():.2f}, {X_digits.max():.2f}]\")\n", "print(f\"Scaled data range: [{X_digits_scaled.min():.2f}, {X_digits_scaled.max():.2f}]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train-test split\n", "X_train_digits, X_test_digits, y_train_digits, y_test_digits = train_test_split(\n", "    X_digits_scaled, y_digits, test_size=0.2, random_state=42, stratify=y_digits\n", ")\n", "\n", "print(f\"Training set: {X_train_digits.shape}\")\n", "print(f\"Test set: {X_test_digits.shape}\")\n", "print(f\"Training class distribution: {np.bincount(y_train_digits)}\")\n", "print(f\"Test class distribution: {np.bincount(y_test_digits)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train MLP Classifier\n", "print(\"🔍 Training MLP on Digits Dataset\")\n", "mlp = MLPClassifier(\n", "    hidden_layer_sizes=(100,),  # One hidden layer with 100 neurons\n", "    max_iter=300, \n", "    random_state=42,\n", "    early_stopping=True,\n", "    validation_fraction=0.1\n", ")\n", "mlp.fit(X_train_digits, y_train_digits)\n", "\n", "# Make predictions\n", "y_pred_digits = mlp.predict(X_test_digits)\n", "\n", "# Evaluation\n", "accuracy_digits = accuracy_score(y_test_digits, y_pred_digits)\n", "print(f\"\\nMLP Accuracy: {accuracy_digits:.4f}\")\n", "\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test_digits, y_pred_digits))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for MLP\n", "cm_digits = confusion_matrix(y_test_digits, y_pred_digits)\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(cm_digits, annot=True, fmt='d', cmap='Greens',\n", "            xticklabels=digits.target_names, \n", "            yticklabels=digits.target_names)\n", "plt.title('MLP Confusion Matrix - Digits Classification')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.show()\n", "\n", "# MLP training information\n", "print(f\"\\nMLP Training Information:\")\n", "print(f\"Number of iterations: {mlp.n_iter_}\")\n", "print(f\"Number of layers: {mlp.n_layers_}\")\n", "print(f\"Number of outputs: {mlp.n_outputs_}\")\n", "print(f\"Loss: {mlp.loss_:.6f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize some predictions\n", "# Show some correct and incorrect predictions\n", "correct_mask = y_test_digits == y_pred_digits\n", "incorrect_mask = ~correct_mask\n", "\n", "# Get indices for correct and incorrect predictions\n", "correct_indices = np.where(correct_mask)[0][:5]\n", "incorrect_indices = np.where(incorrect_mask)[0][:5]\n", "\n", "fig, axes = plt.subplots(2, 5, figsize=(15, 6))\n", "\n", "# Show correct predictions\n", "for i, idx in enumerate(correct_indices):\n", "    # Reshape back to 8x8 image\n", "    image = X_test_digits[idx].reshape(8, 8)\n", "    axes[0, i].imshow(image, cmap='gray')\n", "    axes[0, i].set_title(f'✓ True: {y_test_digits[idx]}, Pred: {y_pred_digits[idx]}')\n", "    axes[0, i].axis('off')\n", "\n", "# Show incorrect predictions\n", "for i, idx in enumerate(incorrect_indices):\n", "    if i < len(incorrect_indices):\n", "        image = X_test_digits[idx].reshape(8, 8)\n", "        axes[1, i].imshow(image, cmap='gray')\n", "        axes[1, i].set_title(f'✗ True: {y_test_digits[idx]}, Pred: {y_pred_digits[idx]}')\n", "        axes[1, i].axis('off')\n", "    else:\n", "        axes[1, i].axis('off')\n", "\n", "plt.suptitle('MLP Predictions: Correct (Top) vs Incorrect (Bottom)')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create summary comparison\n", "summary_data = {\n", "    'Model': ['<PERSON><PERSON><PERSON><PERSON>', 'M<PERSON>'],\n", "    'Dataset': ['Breast Cancer', 'Digits'],\n", "    'Task': ['Binary Classification', 'Multi-class Classification'],\n", "    'Features': [X_bc.shape[1], X_digits.shape[1]],\n", "    'Classes': [len(data_bc.target_names), len(digits.target_names)],\n", "    'Accuracy': [accuracy_bc, accuracy_digits],\n", "    'Architecture': ['Single Layer', '1 Hidden Layer (100 neurons)']\n", "}\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "\n", "print(\"NEURAL NETWORKS COMPARISON\")\n", "print(\"=\" * 60)\n", "print(summary_df.to_string(index=False))\n", "\n", "# Visualization\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Accuracy comparison\n", "models = ['<PERSON><PERSON>pt<PERSON>\\n(Breast Cancer)', 'MLP\\n(Digits)']\n", "accuracies = [accuracy_bc, accuracy_digits]\n", "colors = ['lightblue', 'lightgreen']\n", "\n", "bars = axes[0].bar(models, accuracies, color=colors, alpha=0.8)\n", "axes[0].set_ylabel('Accuracy')\n", "axes[0].set_title('Model Accuracy Comparison')\n", "axes[0].set_ylim(0, 1)\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Add accuracy values on bars\n", "for bar, acc in zip(bars, accuracies):\n", "    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n", "                f'{acc:.4f}', ha='center', va='bottom')\n", "\n", "# Dataset complexity comparison\n", "datasets = ['Breast Cancer', 'Digits']\n", "features = [X_bc.shape[1], X_digits.shape[1]]\n", "classes = [len(data_bc.target_names), len(digits.target_names)]\n", "\n", "x = np.arange(len(datasets))\n", "width = 0.35\n", "\n", "axes[1].bar(x - width/2, features, width, label='Features', alpha=0.8)\n", "axes[1].bar(x + width/2, [c*10 for c in classes], width, label='Classes (×10)', alpha=0.8)\n", "axes[1].set_xlabel('Datasets')\n", "axes[1].set_ylabel('Count')\n", "axes[1].set_title('Dataset Complexity')\n", "axes[1].set_xticks(x)\n", "axes[1].set_xticklabels(datasets)\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"KEY INSIGHTS:\")\n", "print(\"=\" * 60)\n", "print(f\"• Perceptron achieved {accuracy_bc:.1%} accuracy on binary classification\")\n", "print(f\"• MLP achieved {accuracy_digits:.1%} accuracy on 10-class digit recognition\")\n", "print(f\"• MLP's hidden layer enables learning complex non-linear patterns\")\n", "print(f\"• Both models benefit significantly from feature scaling\")\n", "print(f\"• Neural networks are effective for both binary and multi-class problems\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}