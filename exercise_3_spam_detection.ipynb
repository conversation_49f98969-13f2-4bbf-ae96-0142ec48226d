{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Exercise 3 - Spam Detection with TF-IDF and ML Classifiers\n",
    "**Student:** 22BCE7897 <PERSON><PERSON><PERSON><PERSON>\n",
    "\n",
    "This notebook implements spam detection using:\n",
    "- TF-IDF Vectorization\n",
    "- Multiple Machine Learning Classifiers:\n",
    "  - Logistic Regression\n",
    "  - Random Forest\n",
    "  - Gradient Boosting\n",
    "  - K-Nearest Neighbors"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from sklearn.model_selection import train_test_split\n",
    "from sklearn.feature_extraction.text import TfidfVectorizer\n",
    "from sklearn.metrics import accuracy_score, classification_report, roc_auc_score, confusion_matrix\n",
    "from sklearn.linear_model import LogisticRegression\n",
    "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n",
    "from sklearn.neighbors import KNeighborsClassifier\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set plotting style\n",
    "plt.style.use('default')\n",
    "plt.rcParams['figure.figsize'] = (12, 6)\n",
    "sns.set_palette(\"husl\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Load and Explore Dataset"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create synthetic spam dataset for demonstration\n",
    "# (Original code expects '/content/combined_data.csv')\n",
    "\n",
    "np.random.seed(42)\n",
    "\n",
    "# Sample spam and ham messages\n",
    "spam_messages = [\n",
    "    \"Get rich quick! Click here now!\",\n",
    "    \"Free money! No strings attached!\",\n",
    "    \"Congratulations! You've won $1000000!\",\n",
    "    \"Buy now! Limited time offer!\",\n",
    "    \"Urgent! Your account will be closed!\",\n",
    "    \"Free pills! Order now!\",\n",
    "    \"Make money fast! Work from home!\",\n",
    "    \"Click here for amazing deals!\",\n",
    "    \"You are a winner! Claim your prize!\",\n",
    "    \"Lose weight fast! Miracle cure!\"\n",
    "] * 500  # Repeat to create larger dataset\n",
    "\n",
    "ham_messages = [\n",
    "    \"Hi, how are you doing today?\",\n",
    "    \"Meeting scheduled for tomorrow at 3pm\",\n",
    "    \"Thanks for your help with the project\",\n",
    "    \"Can you send me the report?\",\n",
    "    \"Happy birthday! Hope you have a great day\",\n",
    "    \"The weather is nice today\",\n",
    "    \"Let's grab lunch sometime\",\n",
    "    \"Good morning! Have a productive day\",\n",
    "    \"The presentation went well\",\n",
    "    \"See you at the conference next week\"\n",
    "] * 500  # Repeat to create larger dataset\n",
    "\n",
    "# Create DataFrame\n",
    "messages = spam_messages + ham_messages\n",
    "labels = [1] * len(spam_messages) + [0] * len(ham_messages)  # 1 = spam, 0 = ham\n",
    "\n",
    "# Add some noise to make it more realistic\n",
    "np.random.shuffle(list(zip(messages, labels)))\n",
    "\n",
    "df = pd.DataFrame({\n",
    "    'text': messages,\n",
    "    'label': labels\n",
    "})\n",
    "\n",
    "print(f\"Dataset created with {len(df)} messages\")\n",
    "print(f\"Dataset shape: {df.shape}\")\n",
    "print(\"\\nFirst 5 rows:\")\n",
    "print(df.head())"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Dataset information\n",
    "print(\"Dataset Info:\")\n",
    "print(df.info())\n",
    "print(\"\\nLabel distribution:\")\n",
    "print(df['label'].value_counts())\n",
    "print(\"\\nLabel percentages:\")\n",
    "print(df['label'].value_counts(normalize=True) * 100)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Exploratory Data Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualize label distribution\n",
    "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n",
    "\n",
    "# Count plot\n",
    "sns.countplot(data=df, x='label', ax=axes[0], palette='coolwarm')\n",
    "axes[0].set_title(\"Distribution of Messages (Spam vs. Ham)\")\n",
    "axes[0].set_xlabel(\"Message Type (0=Ham, 1=Spam)\")\n",
    "axes[0].set_ylabel(\"Count\")\n",
    "axes[0].grid(True, alpha=0.3)\n",
    "\n",
    "# Pie chart\n",
    "df['label'].value_counts().plot.pie(\n",
    "    autopct=\"%1.1f%%\", \n",
    "    colors=['lightblue', 'orange'],\n",
    "    labels=['Ham', 'Spam'],\n",
    "    ax=axes[1]\n",
    ")\n",
    "axes[1].set_title(\"Spam vs. Ham Ratio\")\n",
    "axes[1].set_ylabel(\"\")\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Text length analysis\n",
    "df['text_length'] = df['text'].str.len()\n",
    "df['word_count'] = df['text'].str.split().str.len()\n",
    "\n",
    "print(\"Text Statistics by Label:\")\n",
    "print(df.groupby('label')[['text_length', 'word_count']].describe())"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualize text characteristics\n",
    "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n",
    "\n",
    "# Text length distribution\n",
    "for label in [0, 1]:\n",
    "    subset = df[df['label'] == label]\n",
    "    axes[0].hist(subset['text_length'], alpha=0.7, \n",
    "                label=f'{'Ham' if label == 0 else 'Spam'}', bins=20)\n",
    "axes[0].set_xlabel('Text Length (characters)')\n",
    "axes[0].set_ylabel('Frequency')\n",
    "axes[0].set_title('Text Length Distribution')\n",
    "axes[0].legend()\n",
    "axes[0].grid(True, alpha=0.3)\n",
    "\n",
    "# Word count distribution\n",
    "for label in [0, 1]:\n",
    "    subset = df[df['label'] == label]\n",
    "    axes[1].hist(subset['word_count'], alpha=0.7, \n",
    "                label=f'{'Ham' if label == 0 else 'Spam'}', bins=20)\n",
    "axes[1].set_xlabel('Word Count')\n",
    "axes[1].set_ylabel('Frequency')\n",
    "axes[1].set_title('Word Count Distribution')\n",
    "axes[1].legend()\n",
    "axes[1].grid(True, alpha=0.3)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Data Preprocessing and Feature Extraction"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Prepare features and labels\n",
    "X = df['text']\n",
    "y = df['label'].astype(int)\n",
    "\n",
    "print(f\"Features shape: {X.shape}\")\n",
    "print(f\"Labels shape: {y.shape}\")\n",
    "print(f\"Label distribution: {y.value_counts().to_dict()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train-test split (80-20)\n",
    "X_train, X_test, y_train, y_test = train_test_split(\n",
    "    X, y, test_size=0.2, random_state=42, stratify=y\n",
    ")\n",
    "\n",
    "print(f\"Dataset Size: {X.shape}\")\n",
    "print(f\"Training Set Size: {X_train.shape}\")\n",
    "print(f\"Test Set Size: {X_test.shape}\")\n",
    "print(f\"\\nTraining label distribution: {y_train.value_counts().to_dict()}\")\n",
    "print(f\"Test label distribution: {y_test.value_counts().to_dict()}\")"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## TF-IDF Vectorization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# TF-IDF Vectorizer with parameters\n",
    "vectorizer = TfidfVectorizer(\n",
    "    min_df=5,           # Ignore terms that appear in less than 5 documents\n",
    "    max_df=0.9,         # Ignore terms that appear in more than 90% of documents\n",
    "    stop_words='english',  # Remove English stop words\n",
    "    lowercase=True,     # Convert to lowercase\n",
    "    max_features=5000   # Limit to top 5000 features\n",
    ")\n",
    "\n",
    "# Fit and transform training data\n",
    "X_train_vec = vectorizer.fit_transform(X_train)\n",
    "X_test_vec = vectorizer.transform(X_test)\n",
    "\n",
    "print(f\"TF-IDF Matrix Shape:\")\n",
    "print(f\"Training: {X_train_vec.shape}\")\n",
    "print(f\"Test: {X_test_vec.shape}\")\n",
    "print(f\"\\nVocabulary size: {len(vectorizer.vocabulary_)}\")\n",
    "print(f\"Feature names (first 10): {vectorizer.get_feature_names_out()[:10]}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Machine Learning Models"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Function to train, evaluate, and print results for any model\n",
    "def train_evaluate_model(model, model_name):\n",
    "    print(f\"\\n{'='*50}\")\n",
    "    print(f\"{model_name}\")\n",
    "    print(f\"{'='*50}\")\n",
    "    \n",
    "    # Train the model\n",
    "    model.fit(X_train_vec, y_train)\n",
    "    \n",
    "    # Make predictions\n",
    "    train_pred = model.predict(X_train_vec)\n",
    "    test_pred = model.predict(X_test_vec)\n",
    "    \n",
    "    # Calculate accuracies\n",
    "    train_acc = accuracy_score(y_train, train_pred)\n",
    "    test_acc = accuracy_score(y_test, test_pred)\n",
    "    \n",
    "    print(f\"Training Accuracy: {train_acc:.4f}\")\n",
    "    print(f\"Testing Accuracy: {test_acc:.4f}\")\n",
    "    \n",
    "    # Classification report\n",
    "    print(\"\\nClassification Report (Test):\")\n",
    "    print(classification_report(y_test, test_pred, target_names=['Ham', 'Spam']))\n",
    "    \n",
    "    # ROC-AUC (if model supports predict_proba)\n",
    "    if hasattr(model, \"predict_proba\"):\n",
    "        roc_auc = roc_auc_score(y_test, model.predict_proba(X_test_vec)[:, 1])\n",
    "        print(f\"ROC-AUC Score: {roc_auc:.4f}\")\n",
    "    else:\n",
    "        print(\"ROC-AUC Score: Not available for this model\")\n",
    "        roc_auc = None\n",
    "    \n",
    "    return {\n",
    "        'model': model,\n",
    "        'train_acc': train_acc,\n",
    "        'test_acc': test_acc,\n",
    "        'test_pred': test_pred,\n",
    "        'roc_auc': roc_auc\n",
    "    }"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 1. Logistic Regression"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Logistic Regression\n",
    "log_model = LogisticRegression(max_iter=1000, random_state=42)\n",
    "log_results = train_evaluate_model(log_model, \"Logistic Regression\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 2. Random Forest"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Random Forest\n",
    "rf_model = RandomForestClassifier(n_estimators=100, random_state=42)\n",
    "rf_results = train_evaluate_model(rf_model, \"Random Forest\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 3. Gradient Boosting"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Gradient Boosting\n",
    "gb_model = GradientBoostingClassifier(\n",
    "    n_estimators=100, \n",
    "    learning_rate=0.1, \n",
    "    max_depth=3, \n",
    "    random_state=42\n",
    ")\n",
    "gb_results = train_evaluate_model(gb_model, \"Gradient Boosting\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 4. K-Nearest Neighbors"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# K-Nearest Neighbors\n",
    "knn_model = KNeighborsClassifier(\n",
    "    n_neighbors=5, \n",
    "    weights='distance', \n",
    "    n_jobs=-1\n",
    ")\n",
    "knn_results = train_evaluate_model(knn_model, \"K-Nearest Neighbors\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Model Comparison and Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Collect all results\n",
    "all_results = {\n",
    "    'Logistic Regression': log_results,\n",
    "    'Random Forest': rf_results,\n",
    "    'Gradient Boosting': gb_results,\n",
    "    'K-Nearest Neighbors': knn_results\n",
    "}\n",
    "\n",
    "# Create comparison DataFrame\n",
    "comparison_data = []\n",
    "for name, results in all_results.items():\n",
    "    comparison_data.append({\n",
    "        'Model': name,\n",
    "        'Train Accuracy': results['train_acc'],\n",
    "        'Test Accuracy': results['test_acc'],\n",
    "        'ROC-AUC': results['roc_auc'] if results['roc_auc'] else 'N/A'\n",
    "    })\n",
    "\n",
    "comparison_df = pd.DataFrame(comparison_data)\n",
    "print(\"MODEL COMPARISON\")\n",
    "print(\"=\" * 60)\n",
    "print(comparison_df.to_string(index=False))"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualization of model performance\n",
    "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
    "\n",
    "# Accuracy comparison\n",
    "models = list(all_results.keys())\n",
    "train_accs = [all_results[m]['train_acc'] for m in models]\n",
    "test_accs = [all_results[m]['test_acc'] for m in models]\n",
    "\n",
    "x = np.arange(len(models))\n",
    "width = 0.35\n",
    "\n",
    "axes[0, 0].bar(x - width/2, train_accs, width, label='Train', alpha=0.8)\n",
    "axes[0, 0].bar(x + width/2, test_accs, width, label='Test', alpha=0.8)\n",
    "axes[0, 0].set_xlabel('Models')\n",
    "axes[0, 0].set_ylabel('Accuracy')\n",
    "axes[0, 0].set_title('Model Accuracy Comparison')\n",
    "axes[0, 0].set_xticks(x)\n",
    "axes[0, 0].set_xticklabels(models, rotation=45, ha='right')\n",
    "axes[0, 0].legend()\n",
    "axes[0, 0].grid(True, alpha=0.3)\n",
    "\n",
    "# ROC-AUC comparison (excluding KNN)\n",
    "roc_models = [m for m in models if all_results[m]['roc_auc'] is not None]\n",
    "roc_scores = [all_results[m]['roc_auc'] for m in roc_models]\n",
    "\n",
    "axes[0, 1].bar(roc_models, roc_scores, alpha=0.8, color='orange')\n",
    "axes[0, 1].set_xlabel('Models')\n",
    "axes[0, 1].set_ylabel('ROC-AUC Score')\n",
    "axes[0, 1].set_title('ROC-AUC Score Comparison')\n",
    "axes[0, 1].tick_params(axis='x', rotation=45)\n",
    "axes[0, 1].grid(True, alpha=0.3)\n",
    "\n",
    "# Confusion Matrix for best model (highest test accuracy)\n",
    "best_model_name = max(all_results.keys(), key=lambda x: all_results[x]['test_acc'])\n",
    "best_predictions = all_results[best_model_name]['test_pred']\n",
    "\n",
    "cm = confusion_matrix(y_test, best_predictions)\n",
    "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n",
    "            xticklabels=['Ham', 'Spam'], yticklabels=['Ham', 'Spam'],\n",
    "            ax=axes[1, 0])\n",
    "axes[1, 0].set_xlabel('Predicted')\n",
    "axes[1, 0].set_ylabel('Actual')\n",
    "axes[1, 0].set_title(f'Confusion Matrix - {best_model_name}')\n",
    "\n",
    "# Feature importance (for Random Forest)\n",
    "if 'Random Forest' in all_results:\n",
    "    rf_model = all_results['Random Forest']['model']\n",
    "    feature_names = vectorizer.get_feature_names_out()\n",
    "    importances = rf_model.feature_importances_\n",
    "    \n",
    "    # Get top 10 most important features\n",
    "    top_indices = np.argsort(importances)[-10:]\n",
    "    top_features = [feature_names[i] for i in top_indices]\n",
    "    top_importances = importances[top_indices]\n",
    "    \n",
    "    axes[1, 1].barh(range(len(top_features)), top_importances, alpha=0.8)\n",
    "    axes[1, 1].set_yticks(range(len(top_features)))\n",
    "    axes[1, 1].set_yticklabels(top_features)\n",
    "    axes[1, 1].set_xlabel('Importance')\n",
    "    axes[1, 1].set_title('Top 10 Feature Importances (Random Forest)')\n",
    "    axes[1, 1].grid(True, alpha=0.3)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Summary and Conclusions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Final summary\n",
    "print(\"SPAM DETECTION ANALYSIS SUMMARY\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "best_model = max(all_results.keys(), key=lambda x: all_results[x]['test_acc'])\n",
    "best_accuracy = all_results[best_model]['test_acc']\n",
    "\n",
    "print(f\"Dataset: {len(df)} messages ({y.sum()} spam, {len(y) - y.sum()} ham)\")\n",
    "print(f\"Features: {X_train_vec.shape[1]} TF-IDF features\")\n",
    "print(f\"\\nBest Model: {best_model}\")\n",
    "print(f\"Best Test Accuracy: {best_accuracy:.4f}\")\n",
    "\n",
    "print(f\"\\nAll Model Performances:\")\n",
    "for name, results in all_results.items():\n",
    "    roc_str = f\", ROC-AUC: {results['roc_auc']:.4f}\" if results['roc_auc'] else \"\"\n",
    "    print(f\"  {name}: {results['test_acc']:.4f}{roc_str}\")\n",
    "\n",
    "print(f\"\\nKey Insights:\")\n",
    "print(f\"• TF-IDF vectorization effectively captures text features\")\n",
    "print(f\"• {best_model} achieved the highest accuracy\")\n",
    "print(f\"• All models show good performance on this spam detection task\")\n",
    "print(f\"• Feature selection and text preprocessing are crucial for performance\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}