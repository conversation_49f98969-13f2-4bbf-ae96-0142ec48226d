{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exercise 5 - <PERSON><PERSON> and <PERSON><PERSON>\n", "**Student:** 22BCE7897 <PERSON><PERSON><PERSON><PERSON>\n", "\n", "This notebook implements:\n", "1. Support Vector Machine (SVM) on MNIST-like digit dataset\n", "2. Logistic Regression comparison\n", "3. <PERSON><PERSON> for Text Classification on E-commerce dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.svm import SVC\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, confusion_matrix, classification_report\n", "from sklearn.metrics import precision_score, recall_score\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.datasets import make_classification\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (10, 6)\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. SVM vs Logistic Regression on Digit Classification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic digit-like dataset\n", "# (Original code expects MNIST dataset)\n", "\n", "np.random.seed(42)\n", "\n", "# Generate synthetic image-like data (28x28 = 784 features)\n", "n_samples = 2000\n", "n_features = 784  # 28x28 pixels\n", "n_classes = 10    # 10 digits (0-9)\n", "\n", "# Create synthetic dataset\n", "X_digits, y_digits = make_classification(\n", "    n_samples=n_samples,\n", "    n_features=n_features,\n", "    n_informative=100,\n", "    n_redundant=50,\n", "    n_classes=n_classes,\n", "    n_clusters_per_class=1,\n", "    random_state=42\n", ")\n", "\n", "# Normalize pixel values to [0,1] range\n", "X_digits = (X_digits - X_digits.min()) / (X_digits.max() - X_digits.min())\n", "\n", "print(f\"Digit Dataset Shape: {X_digits.shape}\")\n", "print(f\"Target Shape: {y_digits.shape}\")\n", "print(f\"Classes: {sorted(np.unique(y_digits))}\")\n", "print(f\"Class distribution: {np.bincount(y_digits)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle any missing values (though synthetic data shouldn't have any)\n", "imputer = SimpleImputer(strategy='mean')\n", "X_digits_imputed = imputer.fit_transform(X_digits)\n", "\n", "print(f\"Missing values before imputation: {np.isnan(X_digits).sum()}\")\n", "print(f\"Missing values after imputation: {np.isnan(X_digits_imputed).sum()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data into train and validation sets (80-20 split)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_digits_imputed, y_digits, test_size=0.2, random_state=42, stratify=y_digits\n", ")\n", "\n", "print(f\"Training set: {X_train.shape}\")\n", "print(f\"Validation set: {X_val.shape}\")\n", "print(f\"Training label distribution: {np.bincount(y_train)}\")\n", "print(f\"Validation label distribution: {np.bincount(y_val)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Support Vector Machine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train Support Vector Machine\n", "print(\"Training SVM...\")\n", "svm_model = SVC(kernel='rbf', random_state=42)\n", "svm_model.fit(X_train, y_train)\n", "\n", "# Predict on validation set\n", "svm_preds = svm_model.predict(X_val)\n", "\n", "# Calculate accuracy for SVM\n", "svm_accuracy = accuracy_score(y_val, svm_preds)\n", "print(f'SVM Accuracy: {svm_accuracy:.4f}')\n", "\n", "print(\"\\nSVM Classification Report:\")\n", "print(classification_report(y_val, svm_preds))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for SVM\n", "svm_cm = confusion_matrix(y_val, svm_preds)\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(svm_cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=range(10), yticklabels=range(10))\n", "plt.title('SVM Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Logistic Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train Logistic Regression\n", "print(\"Training Logistic Regression...\")\n", "logreg_model = LogisticRegression(max_iter=1000, random_state=42)\n", "logreg_model.fit(X_train, y_train)\n", "\n", "# Predict on validation set\n", "logreg_preds = logreg_model.predict(X_val)\n", "\n", "# Calculate accuracy for Logistic Regression\n", "logreg_accuracy = accuracy_score(y_val, logreg_preds)\n", "print(f'Logistic Regression Accuracy: {logreg_accuracy:.4f}')\n", "\n", "print(\"\\nLogistic Regression Classification Report:\")\n", "print(classification_report(y_val, logreg_preds))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for Logistic Regression\n", "logreg_cm = confusion_matrix(y_val, logreg_preds)\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(logreg_cm, annot=True, fmt='d', cmap='Greens',\n", "            xticklabels=range(10), yticklabels=range(10))\n", "plt.title('Logistic Regression Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare SVM and Logistic Regression\n", "comparison_data = {\n", "    'Model': ['SVM', 'Logistic Regression'],\n", "    'Accuracy': [svm_accuracy, logreg_accuracy],\n", "    'Support Vectors': [getattr(svm_model, 'n_support_', 'N/A'), 'N/A']\n", "}\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "print(\"MODEL COMPARISON - DIGIT CLASSIFICATION\")\n", "print(\"=\" * 50)\n", "print(comparison_df.to_string(index=False))\n", "\n", "# Visualization\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Accuracy comparison\n", "models = ['SVM', 'Logistic Regression']\n", "accuracies = [svm_accuracy, logreg_accuracy]\n", "colors = ['skyblue', 'lightgreen']\n", "\n", "axes[0].bar(models, accuracies, color=colors, alpha=0.8)\n", "axes[0].set_ylabel('Accuracy')\n", "axes[0].set_title('Model Accuracy Comparison')\n", "axes[0].set_ylim(0, 1)\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Add accuracy values on bars\n", "for i, acc in enumerate(accuracies):\n", "    axes[0].text(i, acc + 0.01, f'{acc:.4f}', ha='center', va='bottom')\n", "\n", "# Per-class accuracy comparison\n", "svm_class_acc = np.diag(svm_cm) / np.sum(svm_cm, axis=1)\n", "logreg_class_acc = np.diag(logreg_cm) / np.sum(logreg_cm, axis=1)\n", "\n", "x = np.arange(10)\n", "width = 0.35\n", "\n", "axes[1].bar(x - width/2, svm_class_acc, width, label='SVM', alpha=0.8)\n", "axes[1].bar(x + width/2, logreg_class_acc, width, label='Logistic Regression', alpha=0.8)\n", "axes[1].set_xlabel('Digit Class')\n", "axes[1].set_ylabel('Per-class Accuracy')\n", "axes[1].set_title('Per-class Accuracy Comparison')\n", "axes[1].set_xticks(x)\n", "axes[1].set_xticklabels(range(10))\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Text Classification with <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic e-commerce dataset\n", "# (Original code expects ecommerceDataset.csv)\n", "\n", "np.random.seed(42)\n", "\n", "# Sample product descriptions for different categories\n", "books_texts = [\n", "    \"Great novel with amazing story and characters\",\n", "    \"Educational textbook for computer science students\",\n", "    \"Fiction book with mystery and adventure\",\n", "    \"Biography of famous historical figure\",\n", "    \"Self-help book for personal development\"\n", "] * 500\n", "\n", "clothing_texts = [\n", "    \"Comfortable cotton t-shirt for casual wear\",\n", "    \"Elegant dress for special occasions\",\n", "    \"Warm winter jacket with hood\",\n", "    \"Stylish jeans with perfect fit\",\n", "    \"Professional suit for business meetings\"\n", "] * 400\n", "\n", "electronics_texts = [\n", "    \"High-quality smartphone with great camera\",\n", "    \"Laptop computer for work and gaming\",\n", "    \"Wireless headphones with noise cancellation\",\n", "    \"Smart TV with 4K resolution\",\n", "    \"Gaming console with latest technology\"\n", "] * 450\n", "\n", "household_texts = [\n", "    \"Kitchen appliance for cooking and baking\",\n", "    \"Cleaning supplies for home maintenance\",\n", "    \"Furniture for living room decoration\",\n", "    \"Garden tools for outdoor activities\",\n", "    \"Storage solutions for organization\"\n", "] * 600\n", "\n", "# Combine all texts and labels\n", "all_texts = books_texts + clothing_texts + electronics_texts + household_texts\n", "all_labels = (['Books'] * len(books_texts) + \n", "              ['Clothing & Accessories'] * len(clothing_texts) +\n", "              ['Electronics'] * len(electronics_texts) +\n", "              ['Household'] * len(household_texts))\n", "\n", "# Create DataFrame\n", "ecommerce_df = pd.DataFrame({\n", "    'category': all_labels,\n", "    'text': all_texts\n", "})\n", "\n", "# Shuffle the data\n", "ecommerce_df = ecommerce_df.sample(frac=1, random_state=42).reset_index(drop=True)\n", "\n", "print(f\"E-commerce Dataset Shape: {ecommerce_df.shape}\")\n", "print(f\"\\nCategory distribution:\")\n", "print(ecommerce_df['category'].value_counts())\n", "print(\"\\nFirst 5 rows:\")\n", "print(ecommerce_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for text classification\n", "X_text = ecommerce_df['text']\n", "y_text = ecommerce_df['category']\n", "\n", "# Handle missing values in the text column\n", "X_text = X_text.fillna('')\n", "\n", "print(f\"Text data shape: {X_text.shape}\")\n", "print(f\"Label data shape: {y_text.shape}\")\n", "print(f\"Missing values in text: {X_text.isna().sum()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train-test split\n", "X_train_text, X_test_text, y_train_text, y_test_text = train_test_split(\n", "    X_text, y_text, test_size=0.2, random_state=42, stratify=y_text\n", ")\n", "\n", "print(f\"Training set: {X_train_text.shape}\")\n", "print(f\"Test set: {X_test_text.shape}\")\n", "print(f\"\\nTraining label distribution:\")\n", "print(y_train_text.value_counts())\n", "print(f\"\\nTest label distribution:\")\n", "print(y_test_text.value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TF-IDF Vectorization\n", "vectorizer_nb = TfidfVectorizer(stop_words=\"english\", max_features=5000)\n", "X_train_tfidf = vectorizer_nb.fit_transform(X_train_text)\n", "X_test_tfidf = vectorizer_nb.transform(X_test_text)\n", "\n", "print(f\"TF-IDF Training Matrix Shape: {X_train_tfidf.shape}\")\n", "print(f\"TF-IDF Test Matrix Shape: {X_test_tfidf.shape}\")\n", "print(f\"Vocabulary size: {len(vectorizer_nb.vocabulary_)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Naive <PERSON> Model\n", "nb_model = MultinomialNB()\n", "nb_model.fit(X_train_tfidf, y_train_text)\n", "\n", "# Predict\n", "y_pred_nb = nb_model.predict(X_test_tfidf)\n", "\n", "# Evaluation\n", "accuracy_nb = accuracy_score(y_test_text, y_pred_nb)\n", "precision_nb = precision_score(y_test_text, y_pred_nb, average=\"macro\", zero_division=0)\n", "recall_nb = recall_score(y_test_text, y_pred_nb, average=\"macro\", zero_division=0)\n", "\n", "print(\"NAIVE BAYES TEXT CLASSIFICATION RESULTS\")\n", "print(\"=\" * 50)\n", "print(f\"Accuracy: {accuracy_nb:.2f}\")\n", "print(f\"Precision: {precision_nb:.2f}\")\n", "print(f\"Recall: {recall_nb:.2f}\")\n", "\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test_text, y_pred_nb, zero_division=0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for <PERSON><PERSON>\n", "nb_cm = confusion_matrix(y_test_text, y_pred_nb)\n", "categories = sorted(y_text.unique())\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(nb_cm, annot=True, fmt='d', cmap='Oranges',\n", "            xticklabels=categories, yticklabels=categories)\n", "plt.title('Naive Bayes Confusion Matrix - E-commerce Text Classification')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}