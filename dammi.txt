EX 1
22BCE7897 <PERSON><PERSON><PERSON><PERSON> S
1.
Linear Regression
import
numpy
as
np
import
pandas
as
pd
import
matplotlib.pyplot
as
plt
from
sklearn.linear_model
import
LinearRegression
from
sklearn.metrics
import
mean_squared_error
,
r2_score
# Data
xtrain = np.array
([
2.5
,
5.1
,
3.2
,
8.5
,
3.5
,
1.5
,
9.2
,
5.5
,
8.3
,
2.7
])
.reshape
(
-1
,
1
)
ytrain = np.array
([
21
,
47
,
27
,
75
,
30
,
20
,
88
,
60
,
81
,
25
])
# Model
model = LinearRegression
()
model.fit
(
xtrain
,
ytrain
)
# Prediction
yhat = model.predict
([[
7.5
]])
ypred = model.predict
(
xtrain
)
# Metrics
r2 = r2_score
(
ytrain
,
ypred
)
mse = mean_squared_error
(
ytrain
,
ypred
)
rmse = np.sqrt
(
mse
)
# Output
print
(
"The score for a student who studies for 7.5 hours
is:"
,
yhat
[
0
])
print
(
"R2 Score:"
,
r2
)
print
(
"Mean Squared Error:"
,
mse
)
print
(
"Root Mean Squared Error:"
,
rmse
)
# Plot
plt.scatter
(
xtrain
,
ytrain
,
marker=
'x'
,
c=
'r'
,
label=
'Actual'
)
plt.plot
(
xtrain
,
ypred
,
c=
'b'
,
label=
'Predicted'
)
plt.title
(
"Hours Vs. Score"
)
plt.xlabel
(
"Hours"
)
plt.ylabel
(
"Score"
)
plt.legend
()
plt.show
()
The score for a student who studies for 7.5 hours is: 70.88193970420932
R2 Score: 0.9744798425879783
Mean Squared Error: 16.24715301478953
Root Mean Squared Error: 4.030775733626163
2.
Multiple Linear Regression
# Data: [Study hours, Internal marks, Attendance],
Output: Final exam marks
X = np.array
([
[
4
,
80
,
45
],
[
5
,
90
,
50
],
[
6
,
85
,
55
],
[
7
,
70
,
50
],
[
8
,
75
,
60
],
])
y = np.array
([
50
,
60
,
65
,
60
,
70
])
# Model
model1 = LinearRegression
()
model1.fit
(
X
,
y
)
# Predictions and Metrics
ypred1 = model1.predict
(
X
)
r2_ = r2_score
(
y
,
ypred1
)
mse_ = mean_squared_error
(
y
,
ypred1
)
rmse_ = np.sqrt
(
mse_
)
# Output
print
(
"Coefficients:"
,
model1.coef_
)
print
(
"Intercept:"
,
model1.intercept_
)
print
(
"R2 Score:"
,
r2_
)
print
(
"Mean Squared Error:"
,
mse_
)
print
(
"Root Mean Squared Error:"
,
rmse_
)
Coefficients: [3.79310345 0.40229885 0.47126437]Intercept: -18.448275862068968R2 Score: 0.9973876698014629Mean Squared Error: 0.11494252873563218Root Mean Squared Error: 0.3390317518104052
3.
Multivariate Linear Regression
# Load Data
df = pd.read_csv
(
'/content/all_stocks_5yr.csv'
)
# Clean & Prepare
df = df.iloc
[
1
:]
.reset_index
(
drop=
True
)
df
[
'date'
]
= pd.to_datetime
(
df
[
'date'
])
df = df.sort_values
(
by=
'date'
)
.reset_index
(
drop=
True
)
# Feature & Label
X = df
[[
'open'
,
'high'
,
'low'
]]
y = df
[
'close'
]
# Drop rows with NaN values in X and the correspon
ding rows in y
# This is a simple way to handle missing data.
# For more sophisticated methods, consider imputat
ion.
data = pd.concat
([
X
,
y
],
axis=
1
)
.dropna
()
X = data
[[
'open'
,
'high'
,
'low'
]]
y = data
[
'close'
]
# Model
model = LinearRegression
()
model.fit
(
X
,
y
)
# Predict
yhat = model.predict
(
X
)
# Plot
plt.scatter
(
data.index
,
y
,
marker=
'x'
,
c=
'r'
,
label=
'Actual'
)
# Use data.index for plotting
plt.plot
(
data.index
,
yhat
,
c=
'b'
,
label=
'Predicted'
)
# Use data.index for plotting after dro
plt.title
(
"Stock Price Prediction"
)
plt.xlabel
(
"Day 1 to {}"
.
format
(
len
(
data
)))
# Use the length of the data after dropping NaNs
plt.ylabel
(
"Actual Vs. Predicted Price"
)
plt.legend
()
plt.show
()
Exercise 2
22BCE7897
Vasantha Kumar S
import
pandas
as
pd
import
numpy
as
np
import
matplotlib.pyplot
as
plt
from
sklearn.model_selection
import
train_test_split
from
sklearn.linear_model
import
Ridge
,
Lasso
from
sklearn.preprocessing
import
StandardScaler
from
sklearn.decomposition
import
PCA
from
sklearn.metrics
import
mean_absolute_error
,
mean_squared_error
,
r2_score
url =
"https://archive.ics.uci.edu/ml/machine-learning-d
atabases/wine/wine.data"
df = pd.read_csv
(
url
,
header=
None
)
1.
PCA
# Standardize features
scaler = StandardScaler
()
X_scaled = scaler.fit_transform
(
df.iloc
[:,
1
:])
# Apply PCA
pca = PCA
(
n_components=
2
)
X_pca = pca.fit_transform
(
X_scaled
)
# Variance explained by each component
for
i
,
ratio
in
enumerate
(
pca.explained_variance_ratio_
):
print
(
f
"PC
{
i+
1
}
:
{
ratio
:.4f
}
"
)
# Plotting PCA
plt.figure
(
figsize=
(
8
,
6
))
scatter = plt.scatter
(
X_pca
[:,
0
],
X_pca
[:,
1
],
c=df.iloc
[:,
0
],
cmap=
'viridis'
,
edgecolor=
'k'
)
plt.xlabel
(
'Principal Component 1'
)
plt.ylabel
(
'Principal Component 2'
)
plt.title
(
'PCA on Wine Dataset (First 2 Components)'
)
plt.colorbar
(
scatter
,
label=
'Class Label'
)
plt.grid
(
True
)
plt.show
()
PC1: 0.3620
PC2: 0.1921
2.
Ridge and Lasso Regression
# Use Alcohol as regression target
y_reg = df[1]
X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_reg, test_size=0.2, random_state=42)
# Ridge Regression
ridge = Ridge(alpha=1.0)
ridge.fit(X_train, y_train)
y_pred_ridge = ridge.predict(X_test)
# Lasso Regression
lasso = Lasso(alpha=0.1)
lasso.fit(X_train, y_train)
y_pred_lasso = lasso.predict(X_test)
def
evaluate_model
(
y_true
,
y_pred
,
name
=
"Model"
)
:
print
(
f
"\n
{
name
}
Evaluation:"
)
print
(
f
"MAE:
{
mean_absolute_error
(
y_true
,
y_pred
)
:.4f
}
"
)
print
(
f
"MSE:
{
mean_squared_error
(
y_true
,
y_pred
)
:.4f
}
"
)
print
(
f
"RMSE:
{
np.sqrt
(
mean_squared_error
(
y_true
,
y_pred
))
:.4f
}
"
)
print
(
f
"R2 Score:
{
r2_score
(
y_true
,
y_pred
)
:.4f
}
"
)
# Evaluate both models
evaluate_model
(
y_test
,
y_pred_ridge
,
"Ridge Regression"
)
evaluate_model
(
y_test
,
y_pred_lasso
,
"Lasso Regression"
)
Ridge Regression Evaluation:
MAE: 0.0057
MSE: 0.0000
RMSE: 0.0067
R2 Score: 0.9999
Lasso Regression Evaluation:
MAE: 0.0841
MSE: 0.0089
RMSE: 0.0945
R2 Score: 0.9850
Start coding or generate with AI.
Spam Detection with TF-IDF and ML Classifierskeyboard_arrow_down
# Libraries
import
pandas
as
pd
import
matplotlib.pyplot
as
plt
import
seaborn
as
sns
from
sklearn.model_selection
import
train_test_split
from
sklearn.feature_extraction.text
import
TfidfVectorizer
from
sklearn.metrics
import
accuracy_score
,
classification_report
,
roc_auc_score
from
sklearn.linear_model
import
LogisticRegression
from
sklearn.ensemble
import
RandomForestClassifier
,
GradientBoostingClassifier
from
sklearn.svm
import
SVC
from
sklearn.neighbors
import
KNeighborsClassifier
# Load dataset (you can sample for speed by uncomm
enting)
df = pd.read_csv
(
"/content/combined_data.csv"
)
# df = df.sample(n=5000, random_state=42) # optio
nal sampling to speed up
print
(
df.info
())
print
(
df.head
())
# EDA: Distribution of spam and ham
plt.figure
(
figsize=
(
12
,
5
))
sns.countplot
(
x=
'label'
,
data=df
,
palette=
'coolwarm'
)
plt.title
(
"Distribution of Messages (Spam vs. Ham)"
)
plt.xlabel
(
"Message Type (0=Ham, 1=Spam)"
)
plt.ylabel
(
"Count"
)
plt.show
()
df
[
'label'
]
.value_counts
()
.plot.pie
(
autopct=
"%1.1f%%"
,
colors=
[
'lightblue'
,
'orange'
])
plt.title
(
"Spam vs. Ham Ratio"
)
plt.ylabel
(
""
)
plt.show
()
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 47318 entries, 0 to 47317
Data columns (total 2 columns):
# Column Non-Null Count Dtype
--- ------ -------------- -----
0 label 47318 non-null int64
1 text 47318 non-null object
dtypes: int64(1), object(1)
memory usage: 739.5+ KB
None
label text
0 1 ounce feather bowl hummingbird opec moment ala...
1 1 wulvob get your medircations online qnb ikud v...
2 0 computer connection from cnn com wednesday es...
3 1 university degree obtain a prosperous future m...
4 0 thanks for all your answers guys i know i shou...
<ipython-input-9-db26bebe8266>:10: FutureWarning:
Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend
sns.countplot(x='label', data=df, palette='coolwarm')
# Prepare features and labels
X = df
[
'text'
]
y = df
[
'label'
]
.astype
(
int
)
# Train-test split 80-20
X_train
,
X_test
,
y_train
,
y_test = train_test_split
(
X
,
y
,
test_size=
0.2
,
random_state=
42
)
print
(
"Dataset Size:"
,
X.shape
)
print
(
"Training Set Size:"
,
X_train.shape
)
print
(
"Test Set Size:"
,
X_test.shape
)
Dataset Size: (47318,)Training Set Size: (37854,)Test Set Size: (9464,)
# TF-IDF Vectorizer with min_df and max_df and sto
p words
vectorizer = TfidfVectorizer
(
min_df=
5
,
max_df=
0.9
,
stop_words=
'english'
,
lowercase=
True
)
X_train_vec = vectorizer.fit_transform
(
X_train
)
X_test_vec = vectorizer.transform
(
X_test
)
# Function to train, evaluate, and print results f
or any model
def
train_evaluate_model
(
model
,
model_name
)
:
print
(
f
"\n---
{
model_name
}
---"
)
model.fit
(
X_train_vec
,
y_train
)
train_pred = model.predict
(
X_train_vec
)
test_pred = model.predict
(
X_test_vec
)
train_acc = accuracy_score
(
y_train
,
train_pred
)
test_acc = accuracy_score
(
y_test
,
test_pred
)
print
(
f
"Training Accuracy:
{
train_acc
:.4f
}
"
)
print
(
f
"Testing Accuracy:
{
test_acc
:.4f
}
"
)
print
(
"Classification Report (Test):"
)
print
(
classification_report
(
y_test
,
test_pred
))
# ROC-AUC (check if model supports predict_proba)
if
hasattr
(
model
,
"predict_proba"
):
roc_auc = roc_auc_score
(
y_test
,
model.predict_proba
(
X_test_vec
)[:,
1
])
print
(
f
"ROC-AUC Score:
{
roc_auc
:.4f
}
"
)
else
:
print
(
"ROC-AUC Score: Not available for this model"
)
# Logistic Regression
log_model = LogisticRegression
(
max_iter=
1000
,
random_state=
42
)
train_evaluate_model
(
log_model
,
"Logistic Regression"
)
--- Logistic Regression ---Training Accuracy: 0.9895Testing Accuracy: 0.9864Classification Report (Test): precision recall f1-score support 0 0.99 0.98 0.99 7938 1 0.98 0.99 0.99 8752 accuracy 0.99 16690 macro avg 0.99 0.99 0.99 16690weighted avg 0.99 0.99 0.99 16690ROC-AUC Score: 0.9986
# Random Forest
rf_model = RandomForestClassifier
(
n_estimators=
100
,
random_state=
42
)
train_evaluate_model
(
rf_model
,
"Random Forest"
)
--- Random Forest ---Training Accuracy: 0.9997Testing Accuracy: 0.9850Classification Report (Test): precision recall f1-score support 0 0.98 0.99 0.98 7938 1 0.99 0.98 0.99 8752 accuracy 0.99 16690
macro avg 0.98 0.99 0.98 16690weighted avg 0.99 0.99 0.99 16690ROC-AUC Score: 0.9983
# Gradient Boosting
gb_model = GradientBoostingClassifier
(
n_estimators=
100
,
learning_rate=
0.1
,
max_depth=
3
,
random_state=
42
)
train_evaluate_model
(
gb_model
,
"Gradient Boosting"
)
--- Gradient Boosting ---Training Accuracy: 0.9448Testing Accuracy: 0.9417Classification Report (Test): precision recall f1-score support 0 0.98 0.89 0.94 7938 1 0.91 0.98 0.95 8752 accuracy 0.94 16690 macro avg 0.95 0.94 0.94 16690weighted avg 0.94 0.94 0.94 16690ROC-AUC Score: 0.9883
# K-Nearest Neighbors (with 5 neighbors, distance-
based)
knn_model = KNeighborsClassifier
(
n_neighbors=
5
,
weights=
'distance'
,
n_jobs=
-1
)
train_evaluate_model
(
knn_model
,
"K-Nearest Neighbors"
)
--- K-Nearest Neighbors ---TrainingAccuracy:09998
22BCE7897
Vasantha Kumar S
Tree,Bagging Classifier and Random Forest Classifierkeyboard_arrow_down
Decision Tree Accuracy (Credit Card Default): 0 73
 
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import confusion_matrix, accuracy_score
# Load dataset
url = "https://archive.ics.uci.edu/ml/machine-learning-databases/00350/default%20of%20credit%20card%20clients.xls"
data = pd.read_excel(url, skiprows=1)
# Rename target column
data.rename(columns={'default payment next month': 'Default'}, inplace=True)
# Features and labels
X = data.drop(['ID', 'Default'], axis=1)
y = data['Default']
# Train-test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Train Decision Tree
model = DecisionTreeClassifier(random_state=42)
model.fit(X_train, y_train)
y_pred = model.predict(X_test)
# Confusion Matrix
conf_matrix = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(6, 4))
sns.heatmap(conf_matrix, annot=True, fmt="d", cmap="Blues", xticklabels=["No Default", "Default"], yticklabels=["No Default", "Default"])
plt.xlabel("Predicted")
plt.ylabel("Actual")
plt.title("Confusion Matrix - Credit Card Default")
plt.show()
# Accuracy
accuracy = accuracy_score(y_test, y_pred)
print(f"Decision Tree Accuracy (Credit Card Default): {accuracy:.2f}")
# Load mushroom dataset
columns =
[
"class"
,
"cap-shape"
,
"cap-surface"
,
"cap-color"
,
"bruises"
,
"odor"
,
"gill-attachment"
,
"gill-spacing"
,
"gill-size"
,
"gill-color"
,
"stalk-shape"
,
Decision Tree Accuracy (Mushroom): 0 40
 
"stalk-root"
,
"stalk-surface-above-ring"
,
"stalk-surface-below-ring"
,
"stalk-color-above-ring"
,
"stalk-color-below-ring"
,
"veil-type"
,
"ring-number"
,
"ring-type"
,
"spore-print-color"
,
"population"
,
"habitat"
]
data = pd.read_csv
(
'/content/agaricus-lepiota.data'
,
names=columns
)
# Encode categorical data
X = data.drop
(
"class"
,
axis=
1
)
y = data
[
"class"
]
X_encoded = pd.get_dummies
(
X
)
# Train-test split
X_train
,
X_test
,
y_train
,
y_test = train_test_split
(
X_encoded
,
y
,
test_size=
0.2
,
random_state=
42
)
# Train optimized decision tree
model = DecisionTreeClassifier
(
max_depth=
5
,
min_samples_split=
10
,
random_state=
42
)
model.fit
(
X_train
,
y_train
)
y_pred = model.predict
(
X_test
)
# Confusion Matrix
conf_matrix = confusion_matrix
(
y_test
,
y_pred
)
plt.figure
(
figsize=
(
6
,
4
))
sns.heatmap
(
conf_matrix
,
annot=
True
,
fmt=
"d"
,
cmap=
"Reds"
,
xticklabels=
[
"Edible"
,
"Poisonous"
],
yticklabels=
[
"Edible"
,
"Poisonous"
])
plt.xlabel
(
"Predicted"
)
plt.ylabel
(
"Actual"
)
plt.title
(
"Confusion Matrix - Mushroom"
)
plt.show
()
# Accuracy
accuracy = accuracy_score
(
y_test
,
y_pred
)
print
(
f
"Decision Tree Accuracy (Mushroom):
{
accuracy
:.2f
}
"
)
# Load Otto product dataset
data = pd.read_csv
(
'/content/train.csv'
)
# Preprocessing
X = data.drop
([
"id"
,
"target"
],
axis=
1
)
y = data
[
"target"
]
y_encoded = y.astype
(
"category"
)
.cat.codes
# Train-test split
X_train
,
X_test
,
y_train
,
y_test = train_test_split
(
X
,
y_encoded
,
test_size=
0.2
,
random_state=
42
)
# Train Random Forest
from
sklearn.ensemble
import
RandomForestClassifier
# Import RandomForestClassifier
model = RandomForestClassifier
(
n_estimators=
100
,
max_depth=
5
,
random_state=
42
)
model.fit
(
X_train
,
y_train
)
y_pred = model.predict
(
X_test
)
# Confusion Matrix
conf_matrix = confusion_matrix
(
y_test
,
y_pred
)
plt.figure
(
figsize=
(
8
,
6
))
sns.heatmap
(
conf_matrix
,
annot=
True
,
fmt=
"d"
,
cmap=
"Blues"
)
Random Forest Accuracy (Otto): 0.61
plt.xlabel("Predicted")
plt.ylabel("Actual")
plt.title("Confusion Matrix - Otto Product Classification")
plt.show()
# Accuracy
accuracy = accuracy_score(y_test, y_pred)
print(f"Random Forest Accuracy (Otto): {accuracy:.2f}")
Start coding or generate with AI.
SVM and Naive Bayes
22BCE7897
Vasantha Kumar S
import pandas as pd
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, confusion_matrix
from sklearn.model_selection import train_test_split
from sklearn.impute import SimpleImputer # Import SimpleImputer
import matplotlib.pyplot as plt
import seaborn as sns
# Load the dataset
train_df = pd.read_csv('/content/train.csv')
# Separate features and labels
X = train_df.iloc[:, 1:].values / 255.0 # normalize pixel values to [0,1]
y = train_df.iloc[:, 0].values
# Impute missing values using the mean strategy
imputer = SimpleImputer(strategy='mean')
X_imputed = imputer.fit_transform(X)
# Split data into train and validation sets (80-20 split) using the imputed data
X_train, X_val, y_train, y_val = train_test_split(X_imputed, y, test_size=0.2, random_state=42)
# Train Support Vector Machine
svm_model = SVC()
svm_model.fit(X_train, y_train)
# Predict on validation set
svm_preds = svm_model.predict(X_val)
# Calculate accuracy for SVM
svm_accuracy = accuracy_score(y_val, svm_preds)
print(f'SVM Accuracy: {svm_accuracy:.4f}')
# Confusion Matrix for SVM
svm_cm = confusion_matrix(y_val, svm_preds)
plt.figure(figsize=(8, 6))
sns.heatmap(svm_cm, annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('Actual')
plt.show()
# Train Logistic Regression
logreg_model = LogisticRegression(max_iter=1000)
logreg_model.fit(X_train, y_train) # Use imputed data for Logistic Regression as well
# Predict on validation set
logreg_preds = logreg_model.predict(X_val)
# Calculate accuracy for Logistic Regression
logreg_accuracy = accuracy_score(y_val, logreg_preds)
print(f'Logistic Regression Accuracy: {logreg_accuracy:.4f}')
# Confusion Matrix for Logistic Regression
logreg_cm = confusion_matrix(y_val, logreg_preds)
plt.figure(figsize=(8, 6))
sns.heatmap(logreg_cm, annot=True, fmt='d', cmap='Greens')
plt.title('Logistic Regression Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('Actual')
plt.show()
SVM Accuracy: 0.9735
Logistic Regression Accuracy: 0.9185
Text Classification with Naïve Bayes
import
pandas
as
pd
from
sklearn.model_selection
import
train_test_split
from
sklearn.feature_extraction.text
import
TfidfVectorizer
from
sklearn.naive_bayes
import
MultinomialNB
from
sklearn.metrics
import
classification_report
,
accuracy_score
,
precision_score
,
recall_score
# Load your dataset without header
df = pd.read_csv
(
"/content/ecommerceDataset.csv"
,
header=
None
)
# Assuming no header
# Column 0 = Category, Column 1 = Text
X = df.iloc
[:,
1
]
y = df.iloc[:, 0]
# Handle missing values in the text column by replacing them with empty strings
X = X.fillna('')
# Train-test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# TF-IDF Vectorization
vectorizer = TfidfVectorizer(stop_words="english")
X_train_tfidf = vectorizer.fit_transform(X_train)
X_test_tfidf = vectorizer.transform(X_test)
# Naive Bayes Model
model = MultinomialNB()
model.fit(X_train_tfidf, y_train)
# Predict
y_pred = model.predict(X_test_tfidf)
# Evaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, average="macro", zero_division=0)
recall = recall_score(y_test, y_pred, average="macro", zero_division=0)
# Output
print("Accuracy:", round(accuracy, 2))
print("Precision:", round(precision, 2))
print("Recall:", round(recall, 2))
print("\nClassification Report:\n", classification_report(y_test, y_pred, zero_division=0))
Accuracy: 0.94
Precision: 0.95
Recall: 0.94
Classification Report:
precision recall f1-score support
Books 0.97 0.91 0.94 2387
Clothing & Accessories 0.98 0.95 0.96 1744
Electronics 0.96 0.90 0.93 2067
Household 0.91 0.98 0.94 3887
accuracy 0.94 10085
macro avg 0.95 0.94 0.94 10085
weighted avg 0.94 0.94 0.94 10085
Start coding or generate with AI.





import pandas as pd
from sklearn.datasets import load_breast_cancer
from sklearn.linear_model import Perceptron
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score

# Load Dataset
data = load_breast_cancer()
X = data.data
y = data.target

# Split Data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Standardize
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Perceptron
clf = Perceptron(max_iter=1000, tol=1e-3, random_state=42)
clf.fit(X_train_scaled, y_train)

# Evaluation
y_pred = clf.predict(X_test_scaled)
print("🔍 Perceptron on Breast Cancer Dataset")
print("Accuracy:", accuracy_score(y_test, y_pred))
print("Classification Report:\n", classification_report(y_test, y_pred))

from sklearn.datasets import load_digits
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import classification_report, accuracy_score

# Load Dataset
digits = load_digits()
X = digits.data
y = digits.target

# Normalize (MLP works better with scaled data)
scaler = MinMaxScaler()
X_scaled = scaler.fit_transform(X)

# Train-test split
X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)

# MLP Classifier
mlp = MLPClassifier(hidden_layer_sizes=(100,), max_iter=300, random_state=42)
mlp.fit(X_train, y_train)

# Evaluation
y_pred = mlp.predict(X_test)
print("🔍 MLP on Digits Dataset")
print("Accuracy:", accuracy_score(y_test, y_pred))
print("Classification Report:\n", classification_report(y_test, y_pred))



