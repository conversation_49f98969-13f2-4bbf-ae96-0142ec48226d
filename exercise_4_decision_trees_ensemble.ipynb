{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exercise 4 - Decision Trees, Bagging, and Random Forest\n", "**Student:** 22BCE7897 <PERSON><PERSON><PERSON><PERSON>\n", "\n", "This notebook implements:\n", "1. Decision Tree Classifier on Credit Card Default Dataset\n", "2. Decision Tree Classifier on Mushroom Dataset\n", "3. Random Forest Classifier on Otto Product Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import confusion_matrix, accuracy_score, classification_report\n", "from sklearn.preprocessing import LabelEncoder\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (10, 6)\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Decision Tree on Credit Card Default Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic credit card default dataset\n", "# (Original code expects UCI credit card default dataset)\n", "\n", "np.random.seed(42)\n", "n_samples = 5000\n", "\n", "# Generate synthetic credit card data\n", "data = {\n", "    'LIMIT_BAL': np.random.normal(50000, 20000, n_samples),\n", "    'SEX': np.random.choice([1, 2], n_samples),  # 1=male, 2=female\n", "    'EDUCATION': np.random.choice([1, 2, 3, 4], n_samples),\n", "    'MARRIAGE': np.random.choice([1, 2, 3], n_samples),\n", "    'AGE': np.random.randint(20, 70, n_samples),\n", "    'PAY_0': np.random.randint(-2, 9, n_samples),\n", "    'PAY_2': np.random.randint(-2, 9, n_samples),\n", "    'PAY_3': np.random.randint(-2, 9, n_samples),\n", "    'BILL_AMT1': np.random.normal(20000, 15000, n_samples),\n", "    'BILL_AMT2': np.random.normal(18000, 12000, n_samples),\n", "    'PAY_AMT1': np.random.normal(5000, 3000, n_samples),\n", "    'PAY_AMT2': np.random.normal(4500, 2800, n_samples)\n", "}\n", "\n", "# Create target variable (default) based on some logic\n", "credit_df = pd.DataFrame(data)\n", "credit_df['Default'] = ((credit_df['PAY_0'] > 2) | \n", "                       (credit_df['PAY_2'] > 2) | \n", "                       (credit_df['BILL_AMT1'] > credit_df['PAY_AMT1'] * 10)).astype(int)\n", "\n", "print(f\"Credit Card Dataset Shape: {credit_df.shape}\")\n", "print(f\"Default Rate: {credit_df['Default'].mean():.3f}\")\n", "print(\"\\nFirst 5 rows:\")\n", "print(credit_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for modeling\n", "X_credit = credit_df.drop(['Default'], axis=1)\n", "y_credit = credit_df['Default']\n", "\n", "# Train-test split\n", "X_train_credit, X_test_credit, y_train_credit, y_test_credit = train_test_split(\n", "    X_credit, y_credit, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"Training set: {X_train_credit.shape}\")\n", "print(f\"Test set: {X_test_credit.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train Decision Tree\n", "dt_credit = DecisionTreeClassifier(random_state=42, max_depth=10)\n", "dt_credit.fit(X_train_credit, y_train_credit)\n", "y_pred_credit = dt_credit.predict(X_test_credit)\n", "\n", "# Evaluate model\n", "accuracy_credit = accuracy_score(y_test_credit, y_pred_credit)\n", "print(f\"Decision Tree Accuracy (Credit Card Default): {accuracy_credit:.2f}\")\n", "\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test_credit, y_pred_credit, target_names=['No Default', 'Default']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for Credit Card Default\n", "conf_matrix_credit = confusion_matrix(y_test_credit, y_pred_credit)\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix_credit, annot=True, fmt=\"d\", cmap=\"Blues\", \n", "            xticklabels=[\"No Default\", \"Default\"], \n", "            yticklabels=[\"No Default\", \"Default\"])\n", "plt.xlabel(\"Predicted\")\n", "plt.ylabel(\"Actual\")\n", "plt.title(\"Confusion Matrix - Credit Card Default\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Decision Tree on Mushroom Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic mushroom dataset\n", "# (Original code expects mushroom dataset from UCI)\n", "\n", "np.random.seed(42)\n", "n_mushrooms = 2000\n", "\n", "# Define categorical features\n", "cap_shapes = ['bell', 'conical', 'convex', 'flat', 'knobbed', 'sunken']\n", "cap_surfaces = ['fibrous', 'grooves', 'scaly', 'smooth']\n", "cap_colors = ['brown', 'buff', 'cinnamon', 'gray', 'green', 'pink', 'purple', 'red', 'white', 'yellow']\n", "odors = ['almond', 'anise', 'creosote', 'fishy', 'foul', 'musty', 'none', 'pungent', 'spicy']\n", "\n", "mushroom_data = {\n", "    'cap-shape': np.random.choice(cap_shapes, n_mushrooms),\n", "    'cap-surface': np.random.choice(cap_surfaces, n_mushrooms),\n", "    'cap-color': np.random.choice(cap_colors, n_mushrooms),\n", "    'odor': np.random.choice(odors, n_mushrooms),\n", "    'gill-spacing': np.random.choice(['close', 'crowded'], n_mushrooms),\n", "    'gill-size': np.random.choice(['broad', 'narrow'], n_mushrooms),\n", "    'stalk-shape': np.random.choice(['enlarging', 'tapering'], n_mushrooms),\n", "    'ring-type': np.random.choice(['evanescent', 'flaring', 'large', 'none', 'pendant'], n_mushrooms)\n", "}\n", "\n", "mushroom_df = pd.DataFrame(mushroom_data)\n", "\n", "# Create target based on some rules (poisonous vs edible)\n", "# Simplified rule: certain combinations are more likely to be poisonous\n", "poisonous_conditions = (\n", "    (mushroom_df['odor'].isin(['fishy', 'foul', 'musty', 'pungent'])) |\n", "    (mushroom_df['cap-color'].isin(['green', 'purple'])) |\n", "    ((mushroom_df['cap-surface'] == 'scaly') & (mushroom_df['gill-spacing'] == 'close'))\n", ")\n", "\n", "mushroom_df['class'] = np.where(poisonous_conditions, 'p', 'e')  # p=poisonous, e=edible\n", "\n", "print(f\"Mushroom Dataset Shape: {mushroom_df.shape}\")\n", "print(f\"Class distribution:\")\n", "print(mushroom_df['class'].value_counts())\n", "print(\"\\nFirst 5 rows:\")\n", "print(mushroom_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Encode categorical data\n", "X_mushroom = mushroom_df.drop(\"class\", axis=1)\n", "y_mushroom = mushroom_df[\"class\"]\n", "\n", "# One-hot encode categorical features\n", "X_mushroom_encoded = pd.get_dummies(X_mushroom)\n", "\n", "print(f\"Original features: {X_mushroom.shape[1]}\")\n", "print(f\"Encoded features: {X_mushroom_encoded.shape[1]}\")\n", "print(f\"Feature names: {list(X_mushroom_encoded.columns[:10])}...\")  # Show first 10"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train-test split\n", "X_train_mushroom, X_test_mushroom, y_train_mushroom, y_test_mushroom = train_test_split(\n", "    X_mushroom_encoded, y_mushroom, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train optimized decision tree\n", "dt_mushroom = DecisionTreeClassifier(\n", "    max_depth=5, \n", "    min_samples_split=10, \n", "    random_state=42\n", ")\n", "dt_mushroom.fit(X_train_mushroom, y_train_mushroom)\n", "y_pred_mushroom = dt_mushroom.predict(X_test_mushroom)\n", "\n", "# Evaluate model\n", "accuracy_mushroom = accuracy_score(y_test_mushroom, y_pred_mushroom)\n", "print(f\"Decision Tree Accuracy (Mushroom): {accuracy_mushroom:.2f}\")\n", "\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test_mushroom, y_pred_mushroom, target_names=['Edible', 'Poisonous']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for Mushroom\n", "conf_matrix_mushroom = confusion_matrix(y_test_mushroom, y_pred_mushroom)\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix_mushroom, annot=True, fmt=\"d\", cmap=\"Reds\", \n", "            xticklabels=[\"Edible\", \"Poisonous\"], \n", "            yticklabels=[\"Edible\", \"Poisonous\"])\n", "plt.xlabel(\"Predicted\")\n", "plt.ylabel(\"Actual\")\n", "plt.title(\"Confusion Matrix - Mushroom Classification\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON> Forest on Otto Product Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic Otto-like product dataset\n", "# (Original code expects Otto Group Product Classification dataset)\n", "\n", "np.random.seed(42)\n", "n_products = 3000\n", "n_features = 93  # Otto dataset has 93 features\n", "\n", "# Generate synthetic product features\n", "X_otto = np.random.exponential(2, (n_products, n_features))  # Otto features are typically positive\n", "X_otto = np.round(X_otto).astype(int)  # Otto features are counts\n", "\n", "# Create target classes (Class_1 to Class_9)\n", "target_classes = [f'Class_{i}' for i in range(1, 10)]\n", "y_otto_str = np.random.choice(target_classes, n_products)\n", "\n", "# Convert to DataFrame\n", "feature_names = [f'feat_{i}' for i in range(1, n_features + 1)]\n", "otto_df = pd.DataFrame(X_otto, columns=feature_names)\n", "otto_df['target'] = y_otto_str\n", "\n", "print(f\"Otto Product Dataset Shape: {otto_df.shape}\")\n", "print(f\"Target classes: {sorted(otto_df['target'].unique())}\")\n", "print(f\"Class distribution:\")\n", "print(otto_df['target'].value_counts().sort_index())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Preprocessing for Otto dataset\n", "X_otto_features = otto_df.drop([\"target\"], axis=1)\n", "y_otto_target = otto_df[\"target\"]\n", "\n", "# Encode target labels to numeric\n", "label_encoder = LabelEncoder()\n", "y_otto_encoded = label_encoder.fit_transform(y_otto_target)\n", "\n", "print(f\"Features shape: {X_otto_features.shape}\")\n", "print(f\"Target shape: {y_otto_encoded.shape}\")\n", "print(f\"Unique classes: {len(np.unique(y_otto_encoded))}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train-test split\n", "X_train_otto, X_test_otto, y_train_otto, y_test_otto = train_test_split(\n", "    X_otto_features, y_otto_encoded, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train Random Forest\n", "rf_otto = RandomForestClassifier(\n", "    n_estimators=100, \n", "    max_depth=5, \n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "rf_otto.fit(X_train_otto, y_train_otto)\n", "y_pred_otto = rf_otto.predict(X_test_otto)\n", "\n", "# Evaluate model\n", "accuracy_otto = accuracy_score(y_test_otto, y_pred_otto)\n", "print(f\"Random Forest Accuracy (<PERSON>): {accuracy_otto:.2f}\")\n", "\n", "print(\"\\nClassification Report:\")\n", "class_names = [f'Class_{i}' for i in range(1, 10)]\n", "print(classification_report(y_test_otto, y_pred_otto, target_names=class_names))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix for <PERSON>\n", "conf_matrix_otto = confusion_matrix(y_test_otto, y_pred_otto)\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(conf_matrix_otto, annot=True, fmt=\"d\", cmap=\"Blues\",\n", "            xticklabels=class_names, yticklabels=class_names)\n", "plt.xlabel(\"Predicted\")\n", "plt.ylabel(\"Actual\")\n", "plt.title(\"Confusion Matrix - Otto Product Classification\")\n", "plt.xticks(rotation=45)\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}