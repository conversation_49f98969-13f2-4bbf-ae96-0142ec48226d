"""
Exercise 1 - Linear Regression Models
Student: 22BCE7897 <PERSON><PERSON><PERSON><PERSON> S

This file contains three different linear regression implementations:
1. Simple Linear Regression (Hours vs Score)
2. Multiple Linear Regression (Study hours, Internal marks, Attendance vs Final exam marks)
3. Multivariate Linear Regression (Stock Price Prediction)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

def simple_linear_regression():
    """
    1. Simple Linear Regression - Predicting student scores based on study hours
    """
    print("="*50)
    print("1. SIMPLE LINEAR REGRESSION")
    print("="*50)
    
    # Data
    xtrain = np.array([2.5, 5.1, 3.2, 8.5, 3.5, 1.5, 9.2, 5.5, 8.3, 2.7]).reshape(-1, 1)
    ytrain = np.array([21, 47, 27, 75, 30, 20, 88, 60, 81, 25])
    
    # Model
    model = LinearRegression()
    model.fit(xtrain, ytrain)
    
    # Prediction
    yhat = model.predict([[7.5]])
    ypred = model.predict(xtrain)
    
    # Metrics
    r2 = r2_score(ytrain, ypred)
    mse = mean_squared_error(ytrain, ypred)
    rmse = np.sqrt(mse)
    
    # Output
    print(f"The score for a student who studies for 7.5 hours is: {yhat[0]:.2f}")
    print(f"R2 Score: {r2:.4f}")
    print(f"Mean Squared Error: {mse:.4f}")
    print(f"Root Mean Squared Error: {rmse:.4f}")
    
    # Plot
    plt.figure(figsize=(10, 6))
    plt.scatter(xtrain, ytrain, marker='x', c='r', label='Actual', s=100)
    plt.plot(xtrain, ypred, c='b', label='Predicted', linewidth=2)
    plt.title("Hours Vs. Score - Simple Linear Regression")
    plt.xlabel("Hours")
    plt.ylabel("Score")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    return model, r2, mse, rmse

def multiple_linear_regression():
    """
    2. Multiple Linear Regression - Predicting final exam marks
    Data: [Study hours, Internal marks, Attendance] -> Final exam marks
    """
    print("\n" + "="*50)
    print("2. MULTIPLE LINEAR REGRESSION")
    print("="*50)
    
    # Data: [Study hours, Internal marks, Attendance]
    X = np.array([
        [4, 80, 45],
        [5, 90, 50],
        [6, 85, 55],
        [7, 70, 50],
        [8, 75, 60]
    ])
    y = np.array([50, 60, 65, 60, 70])
    
    # Model
    model1 = LinearRegression()
    model1.fit(X, y)
    
    # Predictions and Metrics
    ypred1 = model1.predict(X)
    r2_ = r2_score(y, ypred1)
    mse_ = mean_squared_error(y, ypred1)
    rmse_ = np.sqrt(mse_)
    
    # Output
    print(f"Coefficients: {model1.coef_}")
    print(f"Intercept: {model1.intercept_}")
    print(f"R2 Score: {r2_:.4f}")
    print(f"Mean Squared Error: {mse_:.4f}")
    print(f"Root Mean Squared Error: {rmse_:.4f}")
    
    return model1, r2_, mse_, rmse_

def multivariate_linear_regression():
    """
    3. Multivariate Linear Regression - Stock Price Prediction
    Note: This function expects a stock dataset to be available
    """
    print("\n" + "="*50)
    print("3. MULTIVARIATE LINEAR REGRESSION")
    print("="*50)
    
    try:
        # Load Data (Note: This requires the stock dataset)
        # df = pd.read_csv('/content/all_stocks_5yr.csv')
        print("Note: This function requires a stock dataset file.")
        print("Expected file: 'all_stocks_5yr.csv'")
        print("The function will use synthetic data for demonstration.")
        
        # Create synthetic stock data for demonstration
        np.random.seed(42)
        n_samples = 1000
        
        # Generate synthetic stock data
        open_prices = np.random.normal(100, 10, n_samples)
        high_prices = open_prices + np.random.exponential(2, n_samples)
        low_prices = open_prices - np.random.exponential(2, n_samples)
        close_prices = open_prices + np.random.normal(0, 3, n_samples)
        
        # Create DataFrame
        df = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices
        })
        
        # Feature & Label
        X = df[['open', 'high', 'low']]
        y = df['close']
        
        # Drop rows with NaN values (if any)
        data = pd.concat([X, y], axis=1).dropna()
        X = data[['open', 'high', 'low']]
        y = data['close']
        
        # Model
        model = LinearRegression()
        model.fit(X, y)
        
        # Predict
        yhat = model.predict(X)
        
        # Metrics
        r2 = r2_score(y, yhat)
        mse = mean_squared_error(y, yhat)
        rmse = np.sqrt(mse)
        
        print(f"R2 Score: {r2:.4f}")
        print(f"Mean Squared Error: {mse:.4f}")
        print(f"Root Mean Squared Error: {rmse:.4f}")
        
        # Plot
        plt.figure(figsize=(12, 6))
        plt.scatter(data.index, y, marker='x', c='r', label='Actual', alpha=0.6)
        plt.plot(data.index, yhat, c='b', label='Predicted', alpha=0.8)
        plt.title("Stock Price Prediction - Multivariate Linear Regression")
        plt.xlabel(f"Day 1 to {len(data)}")
        plt.ylabel("Actual Vs. Predicted Price")
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
        
        return model, r2, mse, rmse
        
    except Exception as e:
        print(f"Error in multivariate regression: {e}")
        return None, None, None, None

def main():
    """
    Main function to run all linear regression examples
    """
    print("LINEAR REGRESSION EXERCISES")
    print("Student: 22BCE7897 Vasantha Kumar S")
    print("="*60)
    
    # Run all three regression examples
    model1, r2_1, mse_1, rmse_1 = simple_linear_regression()
    model2, r2_2, mse_2, rmse_2 = multiple_linear_regression()
    model3, r2_3, mse_3, rmse_3 = multivariate_linear_regression()
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY OF RESULTS")
    print("="*50)
    print(f"Simple Linear Regression - R2: {r2_1:.4f}, RMSE: {rmse_1:.4f}")
    print(f"Multiple Linear Regression - R2: {r2_2:.4f}, RMSE: {rmse_2:.4f}")
    if r2_3 is not None:
        print(f"Multivariate Linear Regression - R2: {r2_3:.4f}, RMSE: {rmse_3:.4f}")

if __name__ == "__main__":
    main()
