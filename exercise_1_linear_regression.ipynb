{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exercise 1 - Linear Regression Models\n", "**Student:** 22BCE7897 <PERSON><PERSON><PERSON><PERSON>\n", "\n", "This notebook contains three different linear regression implementations:\n", "1. Simple Linear Regression (Hours vs Score)\n", "2. Multiple Linear Regression (Study hours, Internal marks, Attendance vs Final exam marks)\n", "3. Multivariate Linear Regression (Stock Price Prediction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# Set style for better plots\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (10, 6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Simple Linear Regression\n", "Predicting student scores based on study hours"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data\n", "xtrain = np.array([2.5, 5.1, 3.2, 8.5, 3.5, 1.5, 9.2, 5.5, 8.3, 2.7]).reshape(-1, 1)\n", "ytrain = np.array([21, 47, 27, 75, 30, 20, 88, 60, 81, 25])\n", "\n", "print(f\"Training data shape: X={xtrain.shape}, y={ytrain.shape}\")\n", "print(f\"Study hours: {xtrain.flatten()}\")\n", "print(f\"Scores: {ytrain}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model training\n", "model = LinearRegression()\n", "model.fit(xtrain, ytrain)\n", "\n", "print(f\"Model coefficient (slope): {model.coef_[0]:.4f}\")\n", "print(f\"Model intercept: {model.intercept_:.4f}\")\n", "print(f\"Equation: Score = {model.coef_[0]:.4f} * Hours + {model.intercept_:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Predictions\n", "yhat = model.predict([[7.5]])  # Predict for 7.5 hours\n", "ypred = model.predict(xtrain)  # Predict for all training data\n", "\n", "print(f\"Predicted score for 7.5 hours of study: {yhat[0]:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model evaluation\n", "r2 = r2_score(ytrain, ypred)\n", "mse = mean_squared_error(ytrain, ypred)\n", "rmse = np.sqrt(mse)\n", "\n", "print(f\"R² Score: {r2:.4f}\")\n", "print(f\"Mean Squared Error: {mse:.4f}\")\n", "print(f\"Root Mean Squared Error: {rmse:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(xtrain, ytrain, marker='x', c='red', s=100, label='Actual Data', linewidth=3)\n", "plt.plot(xtrain, ypred, color='blue', linewidth=2, label='Regression Line')\n", "plt.scatter([7.5], yhat, marker='o', c='green', s=150, label=f'Prediction (7.5h = {yhat[0]:.1f})', zorder=5)\n", "\n", "plt.title(\"Simple Linear Regression: Hours vs Score\", fontsize=14, fontweight='bold')\n", "plt.xlabel(\"Study Hours\", fontsize=12)\n", "plt.ylabel(\"Score\", fontsize=12)\n", "plt.legend(fontsize=10)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Multiple Linear Regression\n", "Predicting final exam marks based on study hours, internal marks, and attendance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data: [Study hours, Internal marks, Attendance]\n", "X = np.array([\n", "    [4, 80, 45],\n", "    [5, 90, 50],\n", "    [6, 85, 55],\n", "    [7, 70, 50],\n", "    [8, 75, 60]\n", "])\n", "y = np.array([50, 60, 65, 60, 70])\n", "\n", "# Create DataFrame for better visualization\n", "df_multiple = pd.DataFrame(X, columns=['Study_Hours', 'Internal_Marks', 'Attendance'])\n", "df_multiple['Final_Exam_Marks'] = y\n", "\n", "print(\"Multiple Linear Regression Dataset:\")\n", "print(df_multiple)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model training\n", "model_multiple = LinearRegression()\n", "model_multiple.fit(X, y)\n", "\n", "# Predictions and metrics\n", "ypred_multiple = model_multiple.predict(X)\n", "r2_multiple = r2_score(y, ypred_multiple)\n", "mse_multiple = mean_squared_error(y, ypred_multiple)\n", "rmse_multiple = np.sqrt(mse_multiple)\n", "\n", "print(f\"Coefficients: {model_multiple.coef_}\")\n", "print(f\"Intercept: {model_multiple.intercept_:.4f}\")\n", "print(f\"\\nModel Equation:\")\n", "print(f\"Final_Marks = {model_multiple.coef_[0]:.4f}*Hours + {model_multiple.coef_[1]:.4f}*Internal + {model_multiple.coef_[2]:.4f}*Attendance + {model_multiple.intercept_:.4f}\")\n", "print(f\"\\nR² Score: {r2_multiple:.4f}\")\n", "print(f\"MSE: {mse_multiple:.4f}\")\n", "print(f\"RMSE: {rmse_multiple:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization for Multiple Linear Regression\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Actual vs Predicted\n", "axes[0, 0].scatter(y, ypred_multiple, color='blue', alpha=0.7, s=100)\n", "axes[0, 0].plot([y.min(), y.max()], [y.min(), y.max()], 'r--', linewidth=2)\n", "axes[0, 0].set_xlabel('Actual Final Marks')\n", "axes[0, 0].set_ylabel('Predicted Final Marks')\n", "axes[0, 0].set_title(f'Actual vs Predicted (R² = {r2_multiple:.4f})')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Feature importance (coefficients)\n", "features = ['Study Hours', 'Internal Marks', 'Attendance']\n", "axes[0, 1].bar(features, model_multiple.coef_, color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[0, 1].set_ylabel('Coefficient Value')\n", "axes[0, 1].set_title('Feature Importance (Coefficients)')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Residuals plot\n", "residuals = y - ypred_multiple\n", "axes[1, 0].scatter(ypred_multiple, residuals, color='purple', alpha=0.7, s=100)\n", "axes[1, 0].axhline(y=0, color='red', linestyle='--')\n", "axes[1, 0].set_xlabel('Predicted Values')\n", "axes[1, 0].set_ylabel('Residuals')\n", "axes[1, 0].set_title('Residuals Plot')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Data distribution\n", "axes[1, 1].hist(y, bins=5, alpha=0.7, color='orange', label='Actual')\n", "axes[1, 1].hist(ypred_multiple, bins=5, alpha=0.7, color='blue', label='Predicted')\n", "axes[1, 1].set_xlabel('Final Marks')\n", "axes[1, 1].set_ylabel('Frequency')\n", "axes[1, 1].set_title('Distribution Comparison')\n", "axes[1, 1].legend()\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Multivariate Linear Regression\n", "Stock Price Prediction using synthetic data (since original dataset may not be available)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic stock data for demonstration\n", "np.random.seed(42)\n", "n_samples = 1000\n", "\n", "# Generate realistic stock data\n", "open_prices = np.random.normal(100, 10, n_samples)\n", "high_prices = open_prices + np.random.exponential(2, n_samples)\n", "low_prices = open_prices - np.random.exponential(2, n_samples)\n", "close_prices = open_prices + np.random.normal(0, 3, n_samples)\n", "\n", "# Create DataFrame\n", "df_stock = pd.DataFrame({\n", "    'open': open_prices,\n", "    'high': high_prices,\n", "    'low': low_prices,\n", "    'close': close_prices\n", "})\n", "\n", "print(f\"Stock dataset shape: {df_stock.shape}\")\n", "print(\"\\nFirst 5 rows:\")\n", "print(df_stock.head())\n", "print(\"\\nDataset statistics:\")\n", "print(df_stock.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features and target\n", "X_stock = df_stock[['open', 'high', 'low']]\n", "y_stock = df_stock['close']\n", "\n", "# Remove any NaN values\n", "data_clean = pd.concat([X_stock, y_stock], axis=1).dropna()\n", "X_stock = data_clean[['open', 'high', 'low']]\n", "y_stock = data_clean['close']\n", "\n", "print(f\"Clean dataset shape: {X_stock.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model training\n", "model_stock = LinearRegression()\n", "model_stock.fit(X_stock, y_stock)\n", "\n", "# Predictions and metrics\n", "yhat_stock = model_stock.predict(X_stock)\n", "r2_stock = r2_score(y_stock, yhat_stock)\n", "mse_stock = mean_squared_error(y_stock, yhat_stock)\n", "rmse_stock = np.sqrt(mse_stock)\n", "\n", "print(f\"Stock Price Prediction Model:\")\n", "print(f\"Coefficients: {model_stock.coef_}\")\n", "print(f\"Intercept: {model_stock.intercept_:.4f}\")\n", "print(f\"\\nR² Score: {r2_stock:.4f}\")\n", "print(f\"MSE: {mse_stock:.4f}\")\n", "print(f\"RMSE: {rmse_stock:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization for Stock Price Prediction\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Time series plot (first 200 points for clarity)\n", "sample_size = 200\n", "axes[0, 0].scatter(range(sample_size), y_stock[:sample_size], \n", "                   marker='x', c='red', alpha=0.6, label='Actual', s=20)\n", "axes[0, 0].plot(range(sample_size), yhat_stock[:sample_size], \n", "                color='blue', alpha=0.8, label='Predicted', linewidth=1)\n", "axes[0, 0].set_xlabel('Time (Days)')\n", "axes[0, 0].set_ylabel('Stock Price')\n", "axes[0, 0].set_title('Stock Price Prediction (First 200 days)')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Actual vs Predicted scatter\n", "axes[0, 1].scatter(y_stock, yhat_stock, alpha=0.5, s=10)\n", "axes[0, 1].plot([y_stock.min(), y_stock.max()], [y_stock.min(), y_stock.max()], 'r--', linewidth=2)\n", "axes[0, 1].set_xlabel('Actual Close Price')\n", "axes[0, 1].set_ylabel('Predicted Close Price')\n", "axes[0, 1].set_title(f'Actual vs Predicted (R² = {r2_stock:.4f})')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Feature importance\n", "features_stock = ['Open', 'High', 'Low']\n", "axes[1, 0].bar(features_stock, model_stock.coef_, color=['lightblue', 'lightgreen', 'salmon'])\n", "axes[1, 0].set_ylabel('Coefficient Value')\n", "axes[1, 0].set_title('Feature Importance for Stock Prediction')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Residuals\n", "residuals_stock = y_stock - yhat_stock\n", "axes[1, 1].scatter(yhat_stock, residuals_stock, alpha=0.5, s=10)\n", "axes[1, 1].axhline(y=0, color='red', linestyle='--')\n", "axes[1, 1].set_xlabel('Predicted Values')\n", "axes[1, 1].set_ylabel('Residuals')\n", "axes[1, 1].set_title('Residuals Plot')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary of Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary table\n", "summary_data = {\n", "    'Model': ['Simple Linear Regression', 'Multiple Linear Regression', 'Multivariate Linear Regression'],\n", "    'R² Score': [r2, r2_multiple, r2_stock],\n", "    'RMSE': [rmse, rmse_multiple, rmse_stock],\n", "    'Features': ['1 (Hours)', '3 (Hours, Internal, Attendance)', '3 (Open, High, Low)'],\n", "    'Target': ['Score', 'Final Exam Marks', 'Stock Close Price']\n", "}\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "print(\"SUMMARY OF LINEAR REGRESSION MODELS\")\n", "print(\"=\" * 50)\n", "print(summary_df.to_string(index=False))\n", "\n", "print(\"\\nKey Insights:\")\n", "print(f\"• Best R² Score: {max(r2, r2_multiple, r2_stock):.4f}\")\n", "print(f\"• Simple regression shows strong linear relationship between study hours and scores\")\n", "print(f\"• Multiple regression achieves very high accuracy with multiple predictors\")\n", "print(f\"• Stock prediction model shows the complexity of financial data modeling\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}