"""
Exercise 2 - PCA and Regularization
Student: 22BCE7897 <PERSON><PERSON><PERSON><PERSON> S

This file contains:
1. Principal Component Analysis (PCA) on Wine Dataset
2. Ridge and Lasso Regression comparison
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.linear_model import Ridge, Lasso
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

def load_wine_dataset():
    """
    Load the Wine dataset from UCI repository
    """
    try:
        url = "https://archive.ics.uci.edu/ml/machine-learning-databases/wine/wine.data"
        df = pd.read_csv(url, header=None)
        print(f"Wine dataset loaded successfully. Shape: {df.shape}")
        return df
    except Exception as e:
        print(f"Error loading wine dataset: {e}")
        print("Creating synthetic wine-like dataset for demonstration...")
        
        # Create synthetic dataset similar to wine dataset
        np.random.seed(42)
        n_samples = 178
        n_features = 13
        
        # Generate synthetic features
        data = np.random.randn(n_samples, n_features + 1)
        data[:, 0] = np.random.choice([1, 2, 3], n_samples)  # Class labels
        
        # Make features more realistic (positive values for wine characteristics)
        data[:, 1:] = np.abs(data[:, 1:]) * 10 + np.random.rand(n_samples, n_features) * 5
        
        df = pd.DataFrame(data)
        print(f"Synthetic wine dataset created. Shape: {df.shape}")
        return df

def perform_pca_analysis(df):
    """
    1. Principal Component Analysis on Wine Dataset
    """
    print("="*50)
    print("1. PRINCIPAL COMPONENT ANALYSIS (PCA)")
    print("="*50)
    
    # Standardize features (excluding class label in column 0)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(df.iloc[:, 1:])
    
    # Apply PCA
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X_scaled)
    
    # Variance explained by each component
    print("Variance explained by each principal component:")
    for i, ratio in enumerate(pca.explained_variance_ratio_):
        print(f"PC{i+1}: {ratio:.4f}")
    
    print(f"Total variance explained by first 2 components: {sum(pca.explained_variance_ratio_):.4f}")
    
    # Plotting PCA
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1], 
                         c=df.iloc[:, 0], cmap='viridis', 
                         edgecolor='k', alpha=0.7, s=50)
    plt.xlabel(f'Principal Component 1 ({pca.explained_variance_ratio_[0]:.3f} variance)')
    plt.ylabel(f'Principal Component 2 ({pca.explained_variance_ratio_[1]:.3f} variance)')
    plt.title('PCA on Wine Dataset (First 2 Components)')
    plt.colorbar(scatter, label='Class Label')
    plt.grid(True, alpha=0.3)
    plt.show()
    
    return pca, X_scaled, X_pca

def ridge_lasso_comparison(df, X_scaled):
    """
    2. Ridge and Lasso Regression comparison
    """
    print("\n" + "="*50)
    print("2. RIDGE AND LASSO REGRESSION")
    print("="*50)
    
    # Use first feature as regression target (e.g., Alcohol content)
    y_reg = df.iloc[:, 1]  # First feature after class label
    
    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y_reg, test_size=0.2, random_state=42
    )
    
    # Ridge Regression
    ridge = Ridge(alpha=1.0)
    ridge.fit(X_train, y_train)
    y_pred_ridge = ridge.predict(X_test)
    
    # Lasso Regression
    lasso = Lasso(alpha=0.1)
    lasso.fit(X_train, y_train)
    y_pred_lasso = lasso.predict(X_test)
    
    # Evaluation function
    def evaluate_model(y_true, y_pred, name="Model"):
        print(f"\n{name} Evaluation:")
        mae = mean_absolute_error(y_true, y_pred)
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true, y_pred)
        
        print(f"MAE: {mae:.4f}")
        print(f"MSE: {mse:.4f}")
        print(f"RMSE: {rmse:.4f}")
        print(f"R2 Score: {r2:.4f}")
        
        return {'MAE': mae, 'MSE': mse, 'RMSE': rmse, 'R2': r2}
    
    # Evaluate both models
    ridge_metrics = evaluate_model(y_test, y_pred_ridge, "Ridge Regression")
    lasso_metrics = evaluate_model(y_test, y_pred_lasso, "Lasso Regression")
    
    # Comparison plot
    plt.figure(figsize=(15, 5))
    
    # Ridge predictions
    plt.subplot(1, 3, 1)
    plt.scatter(y_test, y_pred_ridge, alpha=0.7)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    plt.title(f'Ridge Regression\nR² = {ridge_metrics["R2"]:.4f}')
    plt.grid(True, alpha=0.3)
    
    # Lasso predictions
    plt.subplot(1, 3, 2)
    plt.scatter(y_test, y_pred_lasso, alpha=0.7, color='orange')
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    plt.title(f'Lasso Regression\nR² = {lasso_metrics["R2"]:.4f}')
    plt.grid(True, alpha=0.3)
    
    # Coefficients comparison
    plt.subplot(1, 3, 3)
    feature_names = [f'Feature_{i+1}' for i in range(len(ridge.coef_))]
    x_pos = np.arange(len(feature_names))
    
    plt.bar(x_pos - 0.2, ridge.coef_, 0.4, label='Ridge', alpha=0.7)
    plt.bar(x_pos + 0.2, lasso.coef_, 0.4, label='Lasso', alpha=0.7)
    plt.xlabel('Features')
    plt.ylabel('Coefficients')
    plt.title('Ridge vs Lasso Coefficients')
    plt.xticks(x_pos, feature_names, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Feature selection analysis for Lasso
    non_zero_features = np.sum(np.abs(lasso.coef_) > 1e-6)
    print(f"\nFeature Selection Analysis:")
    print(f"Ridge - All {len(ridge.coef_)} features used")
    print(f"Lasso - {non_zero_features} out of {len(lasso.coef_)} features selected")
    
    return ridge, lasso, ridge_metrics, lasso_metrics

def main():
    """
    Main function to run PCA and regularization analysis
    """
    print("PCA AND REGULARIZATION ANALYSIS")
    print("Student: 22BCE7897 Vasantha Kumar S")
    print("="*60)
    
    # Load dataset
    df = load_wine_dataset()
    
    # Perform PCA analysis
    pca, X_scaled, X_pca = perform_pca_analysis(df)
    
    # Ridge and Lasso comparison
    ridge, lasso, ridge_metrics, lasso_metrics = ridge_lasso_comparison(df, X_scaled)
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY OF RESULTS")
    print("="*50)
    print(f"PCA - First 2 components explain {sum(pca.explained_variance_ratio_):.1%} of variance")
    print(f"Ridge Regression - R²: {ridge_metrics['R2']:.4f}, RMSE: {ridge_metrics['RMSE']:.4f}")
    print(f"Lasso Regression - R²: {lasso_metrics['R2']:.4f}, RMSE: {lasso_metrics['RMSE']:.4f}")
    
    # Determine better model
    if ridge_metrics['R2'] > lasso_metrics['R2']:
        print("Ridge Regression performed better on this dataset.")
    else:
        print("Lasso Regression performed better on this dataset.")

if __name__ == "__main__":
    main()
