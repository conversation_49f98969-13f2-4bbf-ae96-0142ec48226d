{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exercise 2 - PCA and Regularization\n", "**Student:** 22BCE7897 <PERSON><PERSON><PERSON><PERSON>\n", "\n", "This notebook contains:\n", "1. Principal Component Analysis (PCA) on Wine Dataset\n", "2. Ridge and Lasso Regression comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import Ridge, Lasso\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (10, 6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Wine Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Wine dataset from UCI repository\n", "try:\n", "    url = \"https://archive.ics.uci.edu/ml/machine-learning-databases/wine/wine.data\"\n", "    df = pd.read_csv(url, header=None)\n", "    print(f\"Wine dataset loaded successfully. Shape: {df.shape}\")\n", "    \n", "    # Add column names\n", "    column_names = ['Class', 'Alcohol', 'Malic_acid', 'Ash', 'Alcalinity_of_ash', \n", "                   'Magnesium', 'Total_phenols', 'Flavanoids', 'Nonflavanoid_phenols',\n", "                   'Proanthocyanins', 'Color_intensity', 'Hue', 'OD280_OD315', 'Proline']\n", "    df.columns = column_names\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading wine dataset: {e}\")\n", "    print(\"Creating synthetic wine-like dataset for demonstration...\")\n", "    \n", "    # Create synthetic dataset\n", "    np.random.seed(42)\n", "    n_samples = 178\n", "    n_features = 13\n", "    \n", "    data = np.random.randn(n_samples, n_features + 1)\n", "    data[:, 0] = np.random.choice([1, 2, 3], n_samples)\n", "    data[:, 1:] = np.abs(data[:, 1:]) * 10 + np.random.rand(n_samples, n_features) * 5\n", "    \n", "    column_names = ['Class', 'Alcohol', 'Malic_acid', 'Ash', 'Alcalinity_of_ash', \n", "                   'Magnesium', 'Total_phenols', 'Flavanoids', 'Nonflavanoid_phenols',\n", "                   'Proanthocyanins', 'Color_intensity', 'Hue', 'OD280_OD315', 'Proline']\n", "    df = pd.DataFrame(data, columns=column_names)\n", "    print(f\"Synthetic wine dataset created. Shape: {df.shape}\")\n", "\n", "print(\"\\nDataset Info:\")\n", "print(df.head())\n", "print(f\"\\nClass distribution:\")\n", "print(df['Class'].value_counts().sort_index())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Principal Component Analysis (PCA)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for PCA (exclude class label)\n", "X = df.iloc[:, 1:]  # All features except class\n", "y = df.iloc[:, 0]   # Class labels\n", "\n", "print(f\"Features shape: {X.shape}\")\n", "print(f\"Labels shape: {y.shape}\")\n", "print(f\"\\nFeature names: {list(X.columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Standardize the features\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "print(f\"Original feature means: {X.mean().round(2).values}\")\n", "print(f\"Scaled feature means: {X_scaled.mean(axis=0).round(2)}\")\n", "print(f\"Scaled feature stds: {X_scaled.std(axis=0).round(2)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply PCA\n", "pca = PCA(n_components=2)\n", "X_pca = pca.fit_transform(X_scaled)\n", "\n", "print(\"PCA Results:\")\n", "print(f\"Explained variance ratio:\")\n", "for i, ratio in enumerate(pca.explained_variance_ratio_):\n", "    print(f\"  PC{i+1}: {ratio:.4f} ({ratio*100:.2f}%)\")\n", "\n", "total_variance = sum(pca.explained_variance_ratio_)\n", "print(f\"\\nTotal variance explained by first 2 components: {total_variance:.4f} ({total_variance*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize PCA results\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Create scatter plot with different colors for each class\n", "classes = sorted(y.unique())\n", "colors = ['red', 'blue', 'green']\n", "markers = ['o', 's', '^']\n", "\n", "for i, class_label in enumerate(classes):\n", "    mask = y == class_label\n", "    plt.scatter(X_pca[mask, 0], X_pca[mask, 1], \n", "               c=colors[i], marker=markers[i], \n", "               label=f'Class {int(class_label)}', \n", "               alpha=0.7, s=60, edgecolor='black', linewidth=0.5)\n", "\n", "plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.3f} variance)', fontsize=12)\n", "plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.3f} variance)', fontsize=12)\n", "plt.title('PCA on Wine Dataset (First 2 Components)', fontsize=14, fontweight='bold')\n", "plt.legend(fontsize=10)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PCA component analysis\n", "feature_names = X.columns\n", "components_df = pd.DataFrame(\n", "    pca.components_.T,\n", "    columns=['PC1', 'PC2'],\n", "    index=feature_names\n", ")\n", "\n", "print(\"PCA Components (Feature Loadings):\")\n", "print(components_df.round(3))\n", "\n", "# Plot feature contributions\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# PC1 contributions\n", "axes[0].barh(range(len(feature_names)), components_df['PC1'], color='skyblue')\n", "axes[0].set_yticks(range(len(feature_names)))\n", "axes[0].set_yticklabels(feature_names, fontsize=10)\n", "axes[0].set_xlabel('Loading')\n", "axes[0].set_title('PC1 Feature Loadings')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# PC2 contributions\n", "axes[1].barh(range(len(feature_names)), components_df['PC2'], color='lightcoral')\n", "axes[1].set_yticks(range(len(feature_names)))\n", "axes[1].set_yticklabels(feature_names, fontsize=10)\n", "axes[1].set_xlabel('Loading')\n", "axes[1].set_title('PC2 Feature Loadings')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Ridge and Lasso Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use Alcohol content as regression target\n", "y_reg = df['Alcohol']\n", "X_reg = X_scaled  # Use standardized features\n", "\n", "print(f\"Regression target (Alcohol): {y_reg.describe()}\")\n", "print(f\"Features shape: {X_reg.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_reg, y_reg, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"Training set: {X_train.shape}\")\n", "print(f\"Test set: {X_test.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ridge Regression\n", "ridge = Ridge(alpha=1.0)\n", "ridge.fit(X_train, y_train)\n", "y_pred_ridge = ridge.predict(X_test)\n", "\n", "# Lasso Regression\n", "lasso = Lasso(alpha=0.1)\n", "lasso.fit(X_train, y_train)\n", "y_pred_lasso = lasso.predict(X_test)\n", "\n", "print(\"Models trained successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluation function\n", "def evaluate_model(y_true, y_pred, name=\"Model\"):\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    r2 = r2_score(y_true, y_pred)\n", "    \n", "    print(f\"\\n{name} Evaluation:\")\n", "    print(f\"  MAE:  {mae:.4f}\")\n", "    print(f\"  MSE:  {mse:.4f}\")\n", "    print(f\"  RMSE: {rmse:.4f}\")\n", "    print(f\"  R²:   {r2:.4f}\")\n", "    \n", "    return {'MAE': mae, 'MSE': mse, 'RMSE': rmse, 'R2': r2}\n", "\n", "# Evaluate both models\n", "ridge_metrics = evaluate_model(y_test, y_pred_ridge, \"Ridge Regression\")\n", "lasso_metrics = evaluate_model(y_test, y_pred_lasso, \"Lasso Regression\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization of Ridge vs Lasso\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Ridge predictions\n", "axes[0, 0].scatter(y_test, y_pred_ridge, alpha=0.7, color='blue', s=60)\n", "axes[0, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', linewidth=2)\n", "axes[0, 0].set_xlabel('Actual Alcohol Content')\n", "axes[0, 0].set_ylabel('Predicted Alcohol Content')\n", "axes[0, 0].set_title(f'Ridge Regression\\nR² = {ridge_metrics[\"R2\"]:.4f}')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Lasso predictions\n", "axes[0, 1].scatter(y_test, y_pred_lasso, alpha=0.7, color='orange', s=60)\n", "axes[0, 1].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', linewidth=2)\n", "axes[0, 1].set_xlabel('Actual Alcohol Content')\n", "axes[0, 1].set_ylabel('Predicted Alcohol Content')\n", "axes[0, 1].set_title(f'Lasso Regression\\nR² = {lasso_metrics[\"R2\"]:.4f}')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Coefficients comparison\n", "x_pos = np.arange(len(feature_names))\n", "width = 0.35\n", "\n", "axes[1, 0].bar(x_pos - width/2, ridge.coef_, width, label='Ridge', alpha=0.7, color='blue')\n", "axes[1, 0].bar(x_pos + width/2, lasso.coef_, width, label='Lasso', alpha=0.7, color='orange')\n", "axes[1, 0].set_xlabel('Features')\n", "axes[1, 0].set_ylabel('Coefficients')\n", "axes[1, 0].set_title('Ridge vs Lasso Coefficients')\n", "axes[1, 0].set_xticks(x_pos)\n", "axes[1, 0].set_xticklabels(feature_names, rotation=45, ha='right')\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Model comparison metrics\n", "metrics = ['MAE', 'MSE', 'RMSE', 'R2']\n", "ridge_values = [ridge_metrics[m] for m in metrics]\n", "lasso_values = [lasso_metrics[m] for m in metrics]\n", "\n", "x_pos = np.arange(len(metrics))\n", "axes[1, 1].bar(x_pos - width/2, ridge_values, width, label='Ridge', alpha=0.7, color='blue')\n", "axes[1, 1].bar(x_pos + width/2, lasso_values, width, label='Lasso', alpha=0.7, color='orange')\n", "axes[1, 1].set_xlabel('Metrics')\n", "axes[1, 1].set_ylabel('Values')\n", "axes[1, 1].set_title('Model Performance Comparison')\n", "axes[1, 1].set_xticks(x_pos)\n", "axes[1, 1].set_xticklabels(metrics)\n", "axes[1, 1].legend()\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature selection analysis\n", "ridge_non_zero = np.sum(np.abs(ridge.coef_) > 1e-6)\n", "lasso_non_zero = np.sum(np.abs(lasso.coef_) > 1e-6)\n", "\n", "print(\"\\nFeature Selection Analysis:\")\n", "print(f\"Ridge Regression:\")\n", "print(f\"  - Uses all {len(ridge.coef_)} features\")\n", "print(f\"  - Non-zero coefficients: {ridge_non_zero}\")\n", "print(f\"  - Average coefficient magnitude: {np.mean(np.abs(ridge.coef_)):.4f}\")\n", "\n", "print(f\"\\nLasso Regression:\")\n", "print(f\"  - Selected {lasso_non_zero} out of {len(lasso.coef_)} features\")\n", "print(f\"  - Zero coefficients: {len(lasso.coef_) - lasso_non_zero}\")\n", "print(f\"  - Average coefficient magnitude: {np.mean(np.abs(lasso.coef_)):.4f}\")\n", "\n", "# Show which features <PERSON><PERSON> selected\n", "selected_features = [feature_names[i] for i, coef in enumerate(lasso.coef_) if abs(coef) > 1e-6]\n", "print(f\"\\nFeatures selected by <PERSON><PERSON>: {selected_features}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Conclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create summary table\n", "summary_data = {\n", "    'Analysis': ['PCA', 'Ridge Regression', 'Lasso Regression'],\n", "    'Key Result': [\n", "        f'{total_variance:.1%} variance in 2 components',\n", "        f'R² = {ridge_metrics[\"R2\"]:.4f}',\n", "        f'R² = {lasso_metrics[\"R2\"]:.4f}'\n", "    ],\n", "    'Features Used': [\n", "        '2 principal components',\n", "        f'All {len(ridge.coef_)} features',\n", "        f'{lasso_non_zero} selected features'\n", "    ],\n", "    'RMSE': [\n", "        'N/A (dimensionality reduction)',\n", "        f'{ridge_metrics[\"RMSE\"]:.4f}',\n", "        f'{lasso_metrics[\"RMSE\"]:.4f}'\n", "    ]\n", "}\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "\n", "print(\"SUMMARY OF PCA AND REGULARIZATION ANALYSIS\")\n", "print(\"=\" * 60)\n", "print(summary_df.to_string(index=False))\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"KEY INSIGHTS:\")\n", "print(\"=\" * 60)\n", "print(f\"• PCA: First 2 components capture {total_variance:.1%} of the variance\")\n", "print(f\"• PCA shows clear separation between wine classes\")\n", "print(f\"• Ridge Regression: Uses all features with regularization\")\n", "print(f\"• Lasso Regression: Performs feature selection, using only {lasso_non_zero} features\")\n", "\n", "if ridge_metrics['R2'] > lasso_metrics['R2']:\n", "    print(f\"• Ridge performs better (R² = {ridge_metrics['R2']:.4f} vs {lasso_metrics['R2']:.4f})\")\n", "else:\n", "    print(f\"• <PERSON><PERSON> performs better (R² = {lasso_metrics['R2']:.4f} vs {ridge_metrics['R2']:.4f})\")\n", "\n", "print(f\"• <PERSON><PERSON>'s feature selection reduces model complexity while maintaining performance\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}